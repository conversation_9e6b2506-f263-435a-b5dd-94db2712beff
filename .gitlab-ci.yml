variables:
  SECRET_DETECTION_DISABLED: 1
  CODE_QUALITY_DISABLED: 1
  TEST_DISABLED: 1
  DOTENV_SECRET_NAME: env.secret
  BROWSER_PERFORMANCE_DISABLED: 1

include:
  - template: Auto-DevOps.gitlab-ci.yml  # https://gitlab.com/gitlab-org/gitlab/blob/master/lib/gitlab/ci/templates/Auto-DevOps.gitlab-ci.yml

.auto-deploy:
  variables:
    # DB_MIGRATE: /cnb/lifecycle/launcher php artisan migrate
    HELM_UPGRADE_EXTRA_ARGS: --values .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
    # K8S_SECRET_DOTENV_FILE: "(Type Variable)"

  before_script:
    - chmod 400 $KUBECONFIG
    - auto-deploy use_kube_context || true
    - auto-deploy ensure_namespace
    - |
      set -ex;
      SED_SCRIPT="; s#path: /srv/data/resources#path: ${VOLUME_NFS_PATH:-/srv/data/resources}#"
      sed "s/secretName: .*/secretName: ${CI_ENVIRONMENT_SLUG}-secret/g ${SED_SCRIPT:-}" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml

canary:
  before_script:
    - |
      set -ex;
      sed "s/secretName: env\.secret/secretName: ${CI_ENVIRONMENT_SLUG}-canary-secret/g; s#path: /srv/data/resources#path: ${VOLUME_NFS_PATH:-/srv/data/resources}#" .gitlab/dotenv-secret-volume.yaml > .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml


build:
  # Use the Docker executor with Docker socket binding
  tags: ["102-stg-docker"]
  services: []
  variables:
    #ALLOW_EOL_SHIMMED_BUILDER: 1
    #AUTO_DEVOPS_BUILD_IMAGE_FORWARDED_CI_VARIABLES: ALLOW_EOL_SHIMMED_BUILDER,PASSPORT_PRIVATE_KEY,LOG_CHANNEL
    AUTO_DEVOPS_BUILD_IMAGE_CNB_BUILDER: heroku/builder:22
    BUILDPACK_URL: heroku/php,heroku/procfile
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"'

code_quality:
  tags: ["102-stg-docker"]

review:
  tags: ["102-stg-docker"]
  variables:
    WEB_CONCURRENCY: 7
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'


.base-production:
  stage: build
  only:
    - main
  variables:
    GIT_STRATEGY: none
  script:
    - cd /srv/api
    - git pull origin main
    - /usr/bin/php8.2 /usr/local/bin/composer install --no-dev
  environment:
    name: production
    url: https://api.topdev.vn

build production web1:
  extends: .base-production
  tags:
    - 115-web1

build production web2:
  extends: .base-production
  tags:
    - 117-web2

build tesing:
  stage: build
  tags:
    - 102-stg
  only:
    - develop
  variables:
    GIT_STRATEGY: none
  script:
    - cd /var/www/api
    - git pull origin develop
    - /usr/bin/php8.2 /usr/bin/composer install
    - /usr/bin/php8.2 artisan queue:restart
    - /usr/bin/php8.2 /opt/cachetool/cachetool.phar opcache:reset --fcgi=/run/php/php8.2-fpm.sock
  environment:
    name: testing
    url: https://api.topdev.asia

