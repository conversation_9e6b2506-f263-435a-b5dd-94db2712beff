<table style="width: 100%; font-family:Helvetica, Arial, sans-serif;font-size:14px;line-height:20px;text-align:left;color:#333333" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td>
                <p style="font-family:Helvetica, Arial, sans-serif; color:#374550">Xin chào <strong>{{$candidate->resume->display_name}}, </strong></p>

                <p style="text-align: justify font-family:Helvetica, Arial, sans-serif;font-size:14px;line-height:20px;text-align:left;color:#333333">
                <strong>TopDev</strong> ghi nhận bạn đã ứng tuyển vị trí <strong><span style="color: #d34127;">{{ $candidate->job->title}}</span></strong> tại <a href="{{ utm_generate($candidate->job->owner->detail_url, ['utm_source' => 'email', 'utm_term' => $candidate->job->owner->display_name, 'utm_medium' => 'candidate-confirm']) }}" style="text-decoration:none" ><strong>{{ $candidate->job->owner->display_name}}</strong></a>. Bạn có thể xem lại chi tiết công việc <a href="{{ utm_generate($candidate->job->detail_url, ['utm_source' => 'email', 'utm_term' => $candidate->job->title, 'utm_medium' => 'candidate-confirm']) }}">tại đây</a>.
                </p>

                @if ($candidate->job->hasHackerrank())
                    @php
                        $hkr_name = empty($candidate->job->hackerrank) ? null : $candidate->job->hackerrank['name'];
                    @endphp
                    @if (!empty($hkr_name))
                        <p style="text-align: justify">
                            <span style="font-family:arial,helvetica,sans-serif;font-size:14px;color:#333333;">
                                <span style="color:#d94127"><em><strong>Notice</strong></em></span>:
                            </span>
                            <span style="font-family:arial,helvetica,sans-serif;font-size:14px;">Vị trí này ưu tiên ứng viên hoàn thành <strong style="color:#000">HackerRank Test</strong> sau khi ứng tuyển. Bài Test <strong>{{ $hkr_name }}</strong> sẽ được gửi đến bạn trong email tiếp theo.</span>
                        </p>
                    @endif
                @endif

                {{-- @if(false)
                <p style="text-align: justify">
                    Bên cạnh đó, để cảm ơn bạn đã tin tưởng lựa chọn sử dụng dịch vụ, TopDev xin gửi tặng bạn 1 vé tham dự sự kiện <strong><a href="https://vietnamwebsummit.com/vi/home/">Vietnam Web Summit 2017 (VWS 2017)</a></strong> do TopDev tổ chức, diễn ra tại HN vào ngày <strong>8/12/2017</strong>
                </p>
                <p style="text-align: justify">
                    Thông tin chi tiết về vé tặng cũng như chương trình VWS 2017 bộ phận hỗ trợ từ sự kiện sẽ gửi đến bạn qua email trong vòng 24h tới.
                </p>
                <p style="text-align: justify">
                    <span style="color: #e44b31">(*)</span> Lưu ý: mỗi ứng viên chỉ nhận được 1 vé mời tham dự chương trình VWS 2017 khi ứng tuyển thành công trên hệ thống của TopDev.
                </p>
                @endif --}}
                <p style="text-align: justify">
                    Đính kèm email này là <a href="{{ !current($candidate->files_cv_url) ? current($candidate->resume->files_cv_url) : current($candidate->files_cv_url)}}" style="text-decoration: none; font-weight: 600;">liên kết đường dẫn hồ sơ</a> bạn đã ứng tuyển cho vị trí này, nếu có sai sót hay cần thay đổi, hãy liên hệ <b>TopDev</b> để được hỗ trợ ngay: <a href="mailto:<EMAIL>"><EMAIL></a> hoặc <strong>(028) 6656 2678</strong>
                </p>

                <p style="font-style: italic"> TopDev Support Team, </p>
            </td>
        </tr>
        @if(!empty($job_suggests))
        <hr style="margin: 20px 0; border-top: 1px solid #cfcfcf;">
        <h2 style="text-align: center;margin: 20px 0;">NHỮNG CÔNG VIỆC HẤP DẪN TẠI <strong style="color:#d34127;">TOPDEV</strong></h2>
        <hr style="margin: 20px 0; border-top: 1px solid #cfcfcf;">
        @endif

        @if (!empty($job_suggestions) && count($job_suggestions) > 0)

        <tr>
            <td>
                <hr style="margin:20px 0;border-top:1px solid #cfcfcf">
                <h2 style="text-align:center;margin:20px 0">NHỮNG CÔNG VIỆC HẤP DẪN TẠI <strong style="color:#d34127;">TOPDEV</strong></h2>
                <table style="margin-left:auto;margin-right:auto;width: 100%" border="0" cellspacing="0" cellpadding="0">
                    <tbody>
                        @foreach ($job_suggestions as $i => $job)

                        <tr style="padding:10px">
                            <td width="25%" align="center" style="padding:15px 10px; @if($i >= 1) {{'border-top:1px solid #efefef;'}} @endif">
                                <a style="font-size:14px;font-weight:bold;color:#333;text-decoration:none" href="{{utm_generate($job->detail_url, ['utm_source' => 'email', 'utm_term' => $job->title, 'utm_medium' => 'candidate-confirm'])}}" target="_blank">
                                    <img width="80" src="{{ current($job->company->image_logo_url) }}">
                                </a>
                            </td>
                            <td width="75%" style="padding:15px 10px;position: relative; @if($i >= 1) {{'border-top:1px solid #efefef;'}} @endif">
                                <div style="color:#545454;">
                                    <a style="text-decoration:none; color:#333;" href="{{utm_generate($job->detail_url, ['utm_source' => 'email', 'utm_term' => $job->title, 'utm_medium' => 'candidate-confirm'])}}" target="_blank">
                                        <span style="display:block;margin-bottom:5px;font-size:14px;font-weight:bold;">{{ $job->title}}</span>
                                        <div style="clear: both;">
                                            <span style="width: 70%;text-align: left;font-size: 13px;display: block;float: left;">{{$job->company->display_name}}</span>
                                            <span style="width: 28%;text-align: right;font-size: 13px;display: block;float: left;">{{ $job->company->address_short_region_list}}</span>
                                        </div>
                                        <div style="color:#d64330;font-size:13px; clear: both;">Salary: {{ last($job->salary) }}</div>
                                    </a>
                                </div>
                                {{-- @if ($job->hasHackerrank())
                                    <img src="https://assets.topdev.vn/static/assets/desktop/images/HackerRank_Badge.png" style="position: absolute;top: 0;right: 0;width: 12%;">
                                @endif --}}
                            </td>
                        </tr>
                        @endforeach

                        <tr style="padding:10px">
                            <td width="25%" align="center" style="padding:15px 10px; border-top:1px solid #efefef;"></td>
                            <td width="75%" style="padding:15px 10px; border-top:1px solid #efefef;">
                                <div style="color:#545454;text-align:right">
                                    <a style="display:block;margin-bottom:5px;font-weight:bold;color:#333;" href="{{utm_generate('https://topdev.vn/it-jobs?cid=' . $candidate->job->company->address_short_region_list, ['utm_source' => 'email', 'utm_term' => $candidate->job->company->address_short_region_list, 'utm_medium' => 'candidate-confirm'])}}" target="_blank">
                                        <span style="font-size:13px;">Tổng hợp việc làm tại {{$candidate->job->company->address_short_region_list}}</span>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <hr style="margin:20px 0;border-top:1px solid #cfcfcf">
            </td>
        </tr>
        @endif

    </tbody>
</table>
