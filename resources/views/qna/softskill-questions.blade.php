<p>
{{ $countQuestions + 1 }}. Bạn hiểu thế nào về điểm mạnh?
</p>
<p>
{{ $countQuestions + 2 }}. V<PERSON>y thế nào là điểm yếu?
</p>
<p>
{{ $countQuestions + 3 }}. Trình bày ưu nhược điểm trong CV –  Đâu là những điều cần lưu ý?
</p>
<p>
{{ $countQuestions + 4 }}. Những câu hỏi thách thức trong buổi phỏng vấn của bạn
</p>
<p>
{{ $countQuestions + 5 }}. Bạn muốn mình là ai và như thế nào trong 5 năm tới?
</p>
<p>
{{ $countQuestions + 6 }}. Em mong muốn mức lương bao nhiêu? Theo em tự đánh giá, với năng lực hiện tại thì mức lương cụ thể nào phù hợp với em?
</p>
<p>
{{ $countQuestions + 7 }}. Đừng lặp lại những thông tin trong CV
</p>
<p>
{{ $countQuestions + 8 }}. Cách trả lời ưu nhược điểm của bản thân bằng tiếng anh
</p>
<p>
{{ $countQuestions + 9 }}. Ai sẽ là người phỏng vấn tôi?
</p>
<p>
{{ $countQuestions + 10 }}. Có thể cho tôi biết rõ hơn về cơ hội phát triển khi tôi làm việc tại đây? Lý do nào nhà tuyển dụng nhận thấy tôi phù hợp?
</p>
<p>
{{ $countQuestions + 11 }}. Tôi khó khăn trong việc xác định các mục tiêu vì chưa rõ những mong muốn cụ thể từ quý công ty/doanh nghiệp, tôi có thể biết thêm về điều này được không?
</p>
<p>
{{ $countQuestions + 12 }}. Người giữ vị trí này trước đây tại sao lại nghỉ việc?
</p>
<p>
{{ $countQuestions + 13 }}. Thách thức lớn nhất đối với người giữ vai trò này là gì?
</p>
<p>
{{ $countQuestions + 14 }}. Hãy kể cho tôi về 1 project mà bạn đã từng làm việc trong 6 tháng qua
</p>
<p>
{{ $countQuestions + 15 }}. Các quyết định về sản phẩm được đưa ra như thế nào?
</p>
<p>
{{ $countQuestions + 16 }}. Công ty đã giúp bạn đạt được mục tiêu nghề nghiệp ra sao?
</p>
<p>
{{ $countQuestions + 17 }}. Văn hóa công ty khác gì với các công ty công nghệ khác?
</p>
<p>
{{ $countQuestions + 18 }}. Cơ hội nào cho nhân viên để học hỏi những điều mới?
</p>
<p>
{{ $countQuestions + 19 }}. Glassdoor: Giữa HackerRank, whiteboarding, paired programming v.v Hiện nay các kiểu phỏng vấn rất đa dạng. Theo chị ứng viên thực sự mong muốn điều gì nhất ở một buổi phỏng vấn ?
</p>
<p>
{{ $countQuestions + 19 }}. Glassdoor: Những nguyên tắc lập trình cơ bản nào mà chị cảm thấy ứng viên cần phải xem lại trước khi tham gia buổi phỏng vấn?
</p>
<p>
{{ $countQuestions + 20 }}. Glassdoor: Vì cấu trúc dữ liệu và các thuật toán rất quan trọng, liệu bạn có thật sự cần một tấm bằng CS để có thể làm việc tại một công ty công nghệ hàng đầu hay không?
</p>
<p>
{{ $countQuestions + 21 }}. Glassdoor: Theo chị, cách thực hành một buổi phỏng vấn tốt nhất sẽ như thế nào?
</p>
<p>
{{ $countQuestions + 22 }}. Glassdoor: Việc luyện tập trước khi tham gia buổi phỏng vấn có ảnh hưởng xấu đến biểu hiện của ứng viên hay không? Mọi người có thường chú ý nhiều đến việc trình bày câu trả lời như nội dung của nó không?
</p>
<p>
{{ $countQuestions + 23 }}. Glassdoor: Theo chị, việc ứng viên không trả lời được câu hỏi trong buổi phỏng vấn có ảnh hưởng đến kết quả hay không? Nếu ứng viên không biết câu trả lời, cách phản hồi tốt nhất là gì?
</p>
<p>
{{ $countQuestions + 23 }}. Glassdoor: Các ứng viên cần chuẩn bị gì cho buổi phỏng vấn không thuần về data structure hay các câu hỏi thuật toán chuyên sâu?
</p>
<p>
{{ $countQuestions + 24 }}. Glassdoor: Bạn sẽ làm gì nếu gặp khó khăn trong buổi phỏng vấn đầu tiên?
</p>
<p>
{{ $countQuestions + 25 }}. Glassdoor: Việc tuyển dụng đã thay đổi như thế nào từ khi chị viết cuốn sách cuối cùng? Theo chị, có xu hướng nào chuẩn bị biến mất không?
</p>
<p>
{{ $countQuestions + 26 }}. Glassdoor: Một số người cho rằng buổi phỏng vấn không thành công là do các yếu tố như thiên vị ngầm và thực tế là chúng thường không nắm bắt chính xác loại công việc bạn đang làm trên cơ sở hàng ngày. Chị có đồng ý không?
</p>
<p>
{{ $countQuestions + 27 }}. Can you introduce yourself? Tell me about yourself (Hãy giới thiệu về bản thân bạn)
</p>
<p>
{{ $countQuestions + 28 }}. What are your strengths and weaknesses? (Điểm mạnh và điểm yếu của bạn là gì?)
</p>
<p>
{{ $countQuestions + 28 }}. Why do you want to apply for this position? (Tại sao bạn lại muốn ứng tuyển vào vị trí này?)
</p>
<p>
{{ $countQuestions + 29 }}. What are your short term goals in your career path? (Các mục tiêu ngắn hạn của bạn trong lộ trình sự nghiệp của bạn?)
</p>
<p>
{{ $countQuestions + 30 }}. What is your expected salary? (Bạn mong đợi mức lương bao nhiêu?)
</p>

