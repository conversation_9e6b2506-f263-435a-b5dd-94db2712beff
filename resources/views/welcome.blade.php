<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>TopDev API</title>
        <META NAME="ROBOTS" CONTENT="NOINDEX, NOFOLLOW">

        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css?family=Nunito:200,600" rel="stylesheet">

        <!-- Styles -->
        <style>
            html, body {
                background-color: #fff;
                color: #636b6f;
                font-family: 'Nunito', sans-serif;
                font-weight: 200;
                height: 100vh;
                margin: 0;
            }

            .full-height {
                height: 100vh;
            }

            .flex-center {
                align-items: center;
                display: flex;
                justify-content: center;
            }

            .position-ref {
                position: relative;
            }

            .top-right {
                position: absolute;
                right: 10px;
                top: 18px;
            }

            .content {
                text-align: center;
            }

            .title {
                font-size: 84px;
            }

            .links > a {
                color: #636b6f;
                padding: 0 25px;
                font-size: 13px;
                font-weight: 600;
                letter-spacing: .1rem;
                text-decoration: none;
                text-transform: uppercase;
            }

            .m-b-md {
                margin-bottom: 30px;
            }
            .m-b-md a{
                color: #e34c31;
                text-decoration: none;
            }
        </style>
    </head>
    <body>
        <div class="flex-center position-ref full-height">
            @if (Route::has('login'))
                <div class="top-right links">
                    @auth
                        <a href="{{ url('/home') }}">Home</a>
                    @else
                        <a href="{{ route('login') }}">Login</a>

                        @if (Route::has('register'))
                            <a href="{{ route('register') }}">Register</a>
                        @endif
                    @endauth
                </div>
            @endif

            <div class="content">
                <div class="title m-b-md">
                    <a href="admin">Applancer Management System</a>
                </div>

                <div class="links">
                    <a href="javascript:void(1)">Applancer</a>
                    <a href="javascript:void(1)">TopDev</a>
                    <a href="javascript:void(1)">Meetup</a>
                    <a href="javascript:void(1)">Reward</a>
                    <a href="javascript:void(1)">Wework</a>
                    <a href="javascript:void(1)">Workflow</a>
                    <a href="javascript:void(1)">Inside</a>
                    <a href="javascript:void(1)">Wiki</a>
                    <a href="javascript:void(1)">Tracking</a>

                </div>
            </div>
        </div>
    </body>
</html>
