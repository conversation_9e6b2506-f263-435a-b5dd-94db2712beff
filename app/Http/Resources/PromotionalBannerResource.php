<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class PromotionalBannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $banners = $this->resource->map(function ($banner) use ($request) {
            return [
                'image' => resize_with_salt(Storage::url($banner['image']), $request['width'], $request['height']),
                'title' => $banner['title'],
                'description' => $banner['description'],
                'hidden' => empty($banner['image']),
                'url' => $banner['url'],
            ];
        });

        if ($banners->isNotEmpty()) {
            return [$banners->random()];
        }

        return [];
    }
}
