<?php

namespace Modules\SearchCandidate\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;

class SearchCandidateServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerFactories();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('SearchCandidate', 'Config/config.php') => config_path('searchcandidate.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('SearchCandidate', 'Config/config.php'), 'searchcandidate'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/searchcandidate');

        $sourcePath = module_path('SearchCandidate', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ],'views');

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/searchcandidate';
        }, \Config::get('view.paths')), [$sourcePath]), 'searchcandidate');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/searchcandidate');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'searchcandidate');
        } else {
            $this->loadTranslationsFrom(module_path('SearchCandidate', 'Resources/lang'), 'searchcandidate');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (! app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('SearchCandidate', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
