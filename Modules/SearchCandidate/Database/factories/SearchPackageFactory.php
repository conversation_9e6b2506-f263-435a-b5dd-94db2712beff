<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;
use Mo<PERSON>les\SearchCandidate\Entities\SearchPackage;

$factory->define(SearchPackage::class, function (Faker $faker) {
    return [
        'name' => $faker->name(),
        'description' => $faker->text(),
        'credit' => $faker->randomNumber(),
        'is_active' => true,
        'created_by' => 1,
        'updated_by' => null,
        'created_at' => $faker->date('Y-m-d H:i:s'),
        'updated_at' => $faker->date('Y-m-d H:i:s'),
        'product_id' => $faker->randomDigit(),
    ];
});
