<?php

namespace Modules\SearchCandidate\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class CompanyUnlockedCvResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        $data = $this->resource;

        return [
            "id" => (int) $data->id,
            "slug" => (string) $data->slug,
            "display_name" => (string) $data->display_name,
            "image_logo" => (string) is_null($data->getFirstMedia('image_logo')) ? null : $data->getFirstMedia('image_logo')->getFullUrl(),
            "unlocked_at" => (string) $data->unlockedCandidates->first()->pivot->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
