<?php

namespace Modules\SearchCandidate\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\User\Entities\User;

class SearchCandidate extends Model
{
    protected $table = 'search_candidates';
    protected $fillable = [];

    public function unlockedByCompany()
    {
        return $this->hasMany(CompanyUnlockSearchCandidate::class, 'search_candidate_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')
            ->resumes();
    }
}
