<?php

namespace Modules\SearchCandidate\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Order\Entities\Order;
use Modules\Admin\Auth\Database\Administrator;
use Modules\SearchCandidate\Entities\CompanyCreditLog;
use Modules\SearchCandidate\Entities\SearchPackage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Modules\SearchCandidate\Entities\CompanySearchPackage.
 *
 * @property int $id
 * @property int $company_id
 * @property int $search_package_id
 * @property string $valid_at
 * @property string $expired_at
 * @property int|null $updated_by
 * @property string|null $updated_by_email
 * @property int $total_credit
 * @property int|null $used_credit
 * @property int|null $remain_credit
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereRemainCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereSearchPackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereTotalCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereUpdatedByEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereUsedCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereValidAt($value)
 * @mixin \Eloquent
 * @property int|null $order_id
 * @property-read Order|null $order
 * @method static \Illuminate\Database\Eloquent\Builder|CompanySearchPackage whereOrderId($value)
 */
class CompanySearchPackage extends Model
{
    protected $table = 'company_search_packages';
    protected $fillable = [
        'company_id',
        'search_package_id',
        'valid_at',
        'expired_at',
        'updated_by',
        'updated_by_email',
        'total_credit',
        'used_credit',
        'remain_credit',
        'company_search_package_id',
        'order_id',
    ];

    
    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function creditLogs()
    {
        return $this->hasMany(CompanyCreditLog::class);
    }

    /**
     * Add credit to company completed order
     *
     * @param int $orderId
     */
    public static function addCreditFromOrder(int $orderId)
    {
        try {
            $order = Order::select('id', 'code', 'user_id', 'created_at', 'last_payment_transaction_id')
                ->with([
                    'user:id,company_id',
                    'items' => function ($query) {
                        return $query->select('order_id', 'quantity', 'product_id')
                                ->whereHas('product', fn($query) => $query->isCredit());
                    }
                ])
                ->whereId($orderId)
                ->firstOrFail();

            if ($order->items->count()) {
                $admin = Administrator::select('id', 'email')->whereEmail('<EMAIL>')->first();
                /**
                 * @var \Carbon\Carbon
                 */
                $paidAt = $order->paid_at ?? $order->created_at;
                $expiresAt = $paidAt->addDays(365);
                $companyId = $order->user->company_id ?? '';
                $orderItems = $order->items->pluck('quantity', 'product_id') ;
                $productIds = $orderItems->keys()->unique();
                $searchPackages = SearchPackage::select('id', 'product_id', 'credit')
                    ->whereIn('product_id', $productIds)
                    ->get();

                DB::beginTransaction();

                $searchPackages->each(function ($package) use ($admin, $orderItems, $companyId, $expiresAt, $orderId) {
                    $email = $admin->email;
                    $adminId = $admin->id;
                    $totalPackages = $orderItems->get($package->product_id);
                    for ($i = 0; $i < $totalPackages; $i++) {
                        $companySearchPackage = CompanySearchPackage::create([
                            'updated_by' => $adminId,
                            'updated_by_email' => $email,
                            'search_package_id' => $package->id,
                            'total_credit' => $package->credit,
                            'remain_credit' => $package->credit,
                            'used_credit' => 0,
                            'company_id' => $companyId,
                            'expired_at' => $expiresAt->startOfDay()->format('Y-m-d H:i:s'),
                            'order_id' => $orderId,
                        ]);

                        $companySearchPackage->creditLogs()->create([
                            'type' => CompanyCreditLog::TYPE_IN,
                            'credit' => $companySearchPackage->total_credit,
                            'created_by' => $adminId,
                            'created_by_email' => $email,
                            'search_package_id' => $package->id,
                            'company_id' => $companyId,
                            'updated_by' => $adminId,
                            'updated_by_email' => $email,
                        ]);
                    }
                });
                DB::commit();
            }
        } catch (\Throwable $ex) {
            DB::rollBack();
            Log::error("CompanySearchPackage@addCreditFromOrder:" . $ex->getMessage(), ['trace' => $ex->getTrace()]);
            app('sentry')->captureException($ex);
        }
    }
}
