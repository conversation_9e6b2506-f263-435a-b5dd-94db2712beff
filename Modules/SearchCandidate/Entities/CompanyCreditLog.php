<?php

namespace Modules\SearchCandidate\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\SearchCandidate\Entities\CompanyCreditLog.
 *
 * @property int $id
 * @property int $company_id
 * @property int $search_package_id
 * @property string $type
 * @property int $credit
 * @property int|null $candidate_id
 * @property string|null $candidate_email
 * @property int|null $created_by
 * @property string|null $created_by_email
 * @property int|null $updated_by
 * @property string|null $updated_by_email
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read SearchPackage $searchPackage
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCandidateEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCandidateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCreatedByEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereSearchPackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereUpdatedByEmail($value)
 * @mixin \Eloquent
 * @property int $company_search_package_id
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCompanySearchPackageId($value)
 * @property string|null $candidate_name
 * @property string|null $created_by_name
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCandidateName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereCreatedByName($value)
 * @property-read CompanySearchPackage $companySearchPackage
 * @property string|null $refund_requested_at
 * @property string|null $refund_approved_at
 * @property int|null $refund_requested_by
 * @property int|null $refund_approved_by
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereRefundApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereRefundApprovedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereRefundRequestedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CompanyCreditLog whereRefundRequestedBy($value)
 */
class CompanyCreditLog extends Model
{
    protected $table = 'company_credit_logs';
    protected $fillable = [
        'company_id',
        'search_package_id',
        'type',
        'credit',
        'candidate_id',
        'candidate_email',
        'created_by',
        'created_by_email',
        'updated_by',
        'updated_by_email',
        'company_search_package_id',
        'refund_requested_at',
        'refund_approved_at',
        'refund_requested_by',
        'refund_approved_by',
    ];

    const TYPE_IN = 'In';
    const TYPE_OUT = 'Out';
    const TYPE_REFUND = 'Refund';
    const TYPE_EXPIRED = 'Expired';

    const REQUEST_EMPTY = 'Empty';
    const REQUEST_REFUND = 'Refund';

    /**
     * return \Illuminate\Database\Eloquent\Relations\BelongsTo.
     */
    public function searchPackage()
    {
        return $this->belongsTo(SearchPackage::class, 'search_package_id');
    }

    /**
     * return \Illuminate\Database\Eloquent\Relations\BelongsTo.
     */
    public function companySearchPackage()
    {
        return $this->belongsTo(CompanySearchPackage::class, 'company_search_package_id');
    }

    /**
     * @return bool
     */
    public function isRequestingRefund()
    {
        return !empty($this->refund_requested_at) && empty($this->refund_approved_at);
    }

    /**
     * @return bool
     */
    public function isApprovedRequestRefund()
    {
        return !empty($this->refund_approved_at);
    }
}
