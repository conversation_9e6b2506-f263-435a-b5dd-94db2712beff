<?php

namespace Modules\SearchCandidate\Entities;

use Illuminate\Database\Eloquent\Model;

/**
 * Modules\SearchCandidate\Entities\SearchPackage.
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $credit
 * @property int $is_active
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage query()
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereCredit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereUpdatedBy($value)
 * @mixin \Eloquent
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage isActive()
 * @property int|null $product_id
 * @property int|null $expires_in
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereExpiresIn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SearchPackage whereProductId($value)
 */
class SearchPackage extends Model
{
    public const PRODUCT_FIELDS = [
        'name',
        'is_active',
        'description',
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'name',
        'description',
        'credit',
        'is_active',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
        'product_id',
    ];

    public function scopeIsActive($query)
    {
        return $query->where('is_active', 1);
    }
}
