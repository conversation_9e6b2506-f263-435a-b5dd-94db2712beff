<?php

namespace Modules\SearchCandidate\Entities;

use Illuminate\Database\Eloquent\Model;
use Modules\Company\Entities\Company;
use Modules\User\Entities\User;

class CompanyUnlockSearchCandidate extends Model
{
    protected $table = 'company_unlock_search_candidates';
    protected $fillable = [];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function searchCandidate()
    {
        return $this->belongsTo(SearchCandidate::class, 'search_candidate_id', 'id');
    }
}
