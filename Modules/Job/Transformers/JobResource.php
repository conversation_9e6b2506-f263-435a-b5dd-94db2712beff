<?php

namespace Modules\Job\Transformers;

use App\Traits\AddDataResource;
use App\Traits\CustomFieldResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Modules\Announcement\Transformers\StatusJobAnnouncementCollection;
use Modules\Company\Transformers\CompanyResource;
use Modules\Job\Entities\Job;
use Redmix0901\ElasticResource\ElasticCollection;
use Redmix0901\ElasticResource\ElasticCollectionTrait;

/**
 * Modules\Job\Transformers\JobResource
 *
 * @property bool $is_top_job_web
 * @property bool $is_distinction_web
 * @property bool $is_basic_plus_web
 * @mixin Job
 */
class JobResource extends Resource
{
    use ElasticCollectionTrait;
    use CustomFieldResource;
    use AddDataResource;

    /**
     * Nếu header có chứa x-topdev-source sau đây thì show lương luôn.
     */
    protected array $skipRequireLoginShowSalary = ['MobileApp', 'FresherPage', 'kicc', 'FrontendV4-Server'];

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray(Request $request): array
    {
        $followed_jobs = (isset((static::$listDataUser)['followed_jobs'])
            && is_array((static::$listDataUser)['followed_jobs']))
            ? (static::$listDataUser)['followed_jobs']
            : null;
        $blacklist_companies = (isset((static::$listDataUser)['blacklist_companies'])
            && !empty((static::$listDataUser)['blacklist_companies']))
            ? (static::$listDataUser)['blacklist_companies']
            : null;

        $locale = $request->locale ?? 'en_US';
        $this->localeUrl($locale, 'detail_url');

        $fields = isset($request->fields['job'])
            ? explode(',', (string) $request->fields['job']) : [];

        $content_arr = (in_array('content_arr', $fields) && !empty($this->content)) ? \transformContent($this->content, $this->id) : [];
        $requirements_arr = (in_array('requirements_arr', $fields) && !empty($this->requirements)) ? $this->requirements : [];
        $responsibilities_arr = (in_array('responsibilities_arr', $fields) && !empty($this->responsibilities)) ? $this->responsibilities : [];

        $salary = $this->formatSalary((array) $this->salary, $locale);

        //-----------get all relation
        $created_at = '';
        $candidate = null;
        $announcement = null;
        $company = null;

        //when get just with resource
        if (static::$shouldGetRelation) {
            $company = app(\Modules\Company\Repositories\Contracts\CompanyRepositoryInterface::class)
                ->searchOnElasticsearch(null, [
                    'ids' => $this->owned_id ?? null,
                ]);
            $company = ((isset($company['hits']['hits'][0]['_source'])
                && !empty($company['hits']['hits'][0]['_source']))
                ? (CompanyResource::listDataUser(static::$listDataUser)->shouldGetRelation(false)
                    ->fromElasticsearch($company)) : null);

            if (isset($request->fields['status_job_announcement'])) {
                $announcement = $this->searchAnnouncement($this->id);
                $announcement = ((isset($announcement['hits']['hits'][0]['_source'])
                    && !empty($announcement['hits']['hits'][0]['_source']))
                    ? StatusJobAnnouncementCollection::fromElasticsearch($announcement) : null);
            }

            if ($this->hasLogin()) {
                $candidate = $this->searchCandidate($this->getUserId(), $this->id);
                if ($candidate != null) {
                    $candidate = CandidateResource::fromElasticsearch($candidate);
                    $created_at = (isset($candidate['created_at']) && !empty($candidate['created_at']))
                        ? $candidate['created_at'] : '';
                }
            }
        } else {
            //static::$shouldGetRelation == true
            //when get just with collection
            $company = isset((static::$dataRelation)['companies'][$this->owned_id])
                ? CompanyResource::listDataUser(static::$listDataUser)->shouldGetRelation(false)
                    ->collection([(static::$dataRelation)['companies'][$this->owned_id]])->first() : null;
            $announcement = (isset((static::$dataRelation)['status_job_announcement'][$this->id]))
                ? new StatusJobAnnouncementCollection((static::$dataRelation)['status_job_announcement'][$this->id])
                : null;
            $candidate = isset((static::$dataRelation)['candidate'][$this->id])
                ? CandidateResource::collection([(static::$dataRelation)['candidate'][$this->id]])->first() : null;
            $created_at = (isset($candidate->created_at) && !empty($candidate->created_at)) ? $candidate->created_at
                : '';
        }

        $contentHtml = $this->content_html_desktop;

        // check if the request is from mobile app, then return the content_html_mobile
        $isMobileApp = false;

        if ($request->header('x-topdev-source') == 'MobileApp'
            || ($request->has('user_agent') && (str_contains($request->user_agent, 'CFNetwork') || str_contains($request->user_agent, 'okhttp')))
        ) {
            $isMobileApp = true;
        }

        if ($request->header('x-topdev-source') == 'MobileApp'
            || ($request->has('user_agent') && (str_contains($request->user_agent, 'CFNetwork') || str_contains($request->user_agent, 'okhttp')))
            || ($request->has('device') && $request->device == 'mobile')
        ) {
            $contentHtml = $this->content_html_mobile;
        }

        return [
            /*
            |--------------------------------------------------------------------------
            | Info basic
            |--------------------------------------------------------------------------
            */
            'id' => $this->when(in_array('id', $fields), (int) $this->id),
            'title' => $this->when(in_array('title', $fields), (string) $this->title),
            'level' => $this->when(in_array('level', $fields), (string) $this->level),
            'is_content_image' => $this->is_content_image,
            'is_content_image_enabled' => $this->is_content_image_enabled,

            /*
            |--------------------------------------------------------------------------
            | Content
            |--------------------------------------------------------------------------
            */
            'content' => $this->when(in_array('content', $fields), (string) $this->content),
            'content_html' => $this->when(in_array('content_html', $fields), (string) $contentHtml),
            'content_str' => $this->when(in_array('content_str', $fields), strip_tags((string) $this->content)),

            'benefits' => $this->when(in_array('benefits', $fields), $isMobileApp ? $this->benefits_original : $this->handleBenefits($this->benefits)),
            'benefits_v2' => $this->when(in_array('benefits_v2', $fields), $this->benefits_v2),

            'requirements' => $this->when(in_array('requirements', $fields), $this->getRequirements()),
            'responsibilities' => $this->when(in_array('responsibilities', $fields), $this->getResponsibilities()),
            'recruiment_process' => $this->when(in_array('recruiment_process', $fields), $isMobileApp ? $this->recruitment_process_original : $this->recruiment_process),

            /*
            |--------------------------------------------------------------------------
            | Company
            |--------------------------------------------------------------------------
            */
            'owned_id' => $this->owned_id,
            // 'company' => empty(preg_grep('/^(latest_jobs_)[0-9]+$/i', isset($request->fields['company']) ? explode(",", $request->fields['company']) : [])) ? ((isset($company['hits']['hits'][0]['_source']) && !empty($company['hits']['hits'][0]['_source'])) ? (CompanyResource::listDataUser(static::$listDataUser)->fromElasticsearch($company)) : null)

            // : $this->when(in_array('company', $fields), array_merge(
            //     (array) $this->company,
            //     [
            //         'detail_url' => frontend_url('companies/' . ($this->company['slug'] ?? null) . '-' . ($this->company['id'] ?? null)),
            //     ])),
            // : $this->when(in_array('company', $fields),
            //     [
            //         'id' => $this->owned_id,
            //     ]

            // ),

            // 'company' => $this->when(in_array('company', $fields) && static::$shouldGetRelation == true,((isset($company['hits']['hits'][0]['_source']) && !empty($company['hits']['hits'][0]['_source'])) ? (CompanyResource::listDataUser(static::$listDataUser)->shouldGetRelation(false)->fromElasticsearch($company)) : null)),
            'company' => $this->when(in_array('company', $fields), $company),

            /*
            |--------------------------------------------------------------------------
            | Skill
            |--------------------------------------------------------------------------
            */
            'extra_skills' => $this->when(in_array('extra_skills', $fields), (array) $this->extra_skills),

            'skills_str' => $this->when(in_array('skills_str', $fields), (string) $this->skills_str),
            'skills_arr' => $this->when(in_array('skills_arr', $fields), (array) $this->skills_arr),
            'skills_ids' => $this->when(in_array('skills_ids', $fields), (array) $this->skills_ids), //---
            'skills' => $this->when(in_array('skills', $fields), (array) $this->skills),

            'experiences_str' => $this->when(in_array('experiences_str', $fields), (string) $this->experiences_str),
            'experiences_arr' => $this->when(in_array('experiences_arr', $fields), (array) $this->experiences_arr),
            'experiences_ids' => $this->when(in_array('experiences_ids', $fields), (array) $this->experiences_ids),//---

            'contract_types_str' => $this->when(in_array('contract_types_str', $fields),
                (string) $this->contract_types_str),
            'contract_types_arr' => $this->when(in_array('contract_types_arr', $fields),
                (array) $this->contract_types_arr),
            'contract_types_ids' => $this->when(in_array('contract_types_ids', $fields),
                (array) $this->contract_types_ids), //---

            'job_types_str' => $this->when(in_array('job_types_str', $fields), (string) $this->job_types_str),
            'job_types_arr' => $this->when(in_array('job_types_arr', $fields), (array) $this->job_types_arr),
            'job_types_ids' => $this->when(in_array('job_types_ids', $fields), (array) $this->job_types_ids), //---
            'job_levels_str' => $this->when(in_array('job_levels_str', $fields), (string) $this->job_levels_str),
            'job_levels_arr' => $this->when(in_array('job_levels_arr', $fields), (array) $this->job_levels_arr),
            'job_levels_ids' => $this->when(in_array('job_levels_ids', $fields), (array) $this->job_levels_ids), //---

            'education_arr' => $this->when(in_array('education_arr', $fields), $this->education_arr),
            'education_str' => $this->when(in_array('education_str', $fields), $this->education_str),
            'education_ids' => $this->when(in_array('education_ids', $fields), $this->education_ids), //---

            'education_major_arr' => $this->when(in_array('education_major_arr', $fields), $this->education_major_arr),
            'education_major_str' => $this->when(in_array('education_major_str', $fields), $this->education_major_str),
            'education_major_ids' => $this->when(in_array('education_major_ids', $fields), $this->education_major_ids), //---

            /*
            |--------------------------------------------------------------------------
            | Address
            |--------------------------------------------------------------------------
            */
            'addresses' => $this->when(in_array('addresses', $fields), (array) $this->addresses),

            /*
            |--------------------------------------------------------------------------
            | Status
            |--------------------------------------------------------------------------
            */
            'status_display' => $this->when(in_array('status_display', $fields), (string) $this->status_display),

            'detail_url' => $this->when(in_array('detail_url', $fields), (string) $this->detail_url),
            'apply_url' => $this->when(in_array('apply_url', $fields), (string) $this->apply_url),
            'job_url' => $this->when(in_array('job_url', $fields), (string) $this->job_url),
            'slug' => $this->when(in_array('slug', $fields), (string) $this->slug),

            /*
            |--------------------------------------------------------------------------
            |
            |--------------------------------------------------------------------------
            */
            'num_followers' => $this->when(in_array('num_followers', $fields) && $this->hasLogin(),
                (int) $this->num_followers),
            'num_viewers' => $this->when(in_array('num_viewers', $fields) && $this->hasLogin(),
                (int) $this->num_viewers),
            'num_candidates' => $this->when(in_array('num_candidates', $fields) && $this->hasLogin(),
                (int) $this->num_candidates),
            'num_ready_candidates' => $this->when(in_array('num_ready_candidates', $fields),
                (int) $this->num_ready_candidates),
            'num_unqualified_candidates' => $this->when(in_array('num_unqualified_candidates', $fields),
                (int) $this->num_unqualified_candidates),

            'salary' => $this->when(in_array('salary', $fields), $salary),
            'features' => $this->when(in_array('features', $fields), (array) $this->features),
            'packages' => $this->when(in_array('packages', $fields), (array) $this->packages),

            'is_free' => $this->when(in_array('is_free', $fields), (bool) $this->is_free),
            'is_basic' => $this->when(in_array('is_basic', $fields), (bool) $this->is_basic),
            'is_basic_plus' => $this->when(in_array('is_basic_plus', $fields), (bool) $this->is_basic_plus),
            'is_distinction' => $this->when(in_array('is_distinction', $fields), (bool) $this->is_distinction),
            'is_top_job_web' => $this->when(in_array('is_top_job_web', $fields), (bool) $this->is_top_job_web),
            'is_distinction_web' => $this->when(in_array('is_distinction_web', $fields),
                (bool) $this->is_distinction_web),
            'is_basic_plus_web' => $this->when(in_array('is_basic_plus_web', $fields), (bool) $this->is_basic_plus_web),

            'schema_job_posting' => $this->when(in_array('schema_job_posting', $fields),
                (string) $this->getSchemaJobPostingAttribute()),
            'is_salary_visible' => (bool) $this->hasLogin(),

            /*
            |--------------------------------------------------------------------------
            | Datetime
            |--------------------------------------------------------------------------
            */
            'created' => $this->when(in_array('created', $fields), (empty($this->created_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->created_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'expires' => $this->when(in_array('expires', $fields), (empty($this->expires_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'modified' => $this->when(in_array('modified', $fields), (empty($this->updated_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->updated_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'closed' => $this->when(in_array('closed', $fields), (empty($this->expires_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->expires_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'published' => $this->when(in_array('published', $fields), (empty($this->published_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->published_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'refreshed' => $this->when(in_array('refreshed', $fields), (empty($this->refreshed_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $this->refreshed_at)->locale($locale)
                        ->diffForHumans(),
                ])),
            'applied' => $this->when(in_array('applied', $fields), (empty($created_at)
                ? null
                : [
                    'date' => Carbon::createFromFormat('Y-m-d H:i:s', $created_at)->format('d-m-Y'),
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $created_at)->format('H:i:s d-m-Y'),
                    'since' => Carbon::createFromFormat('Y-m-d H:i:s', $created_at)->locale($locale)->diffForHumans(),
                ])),
            'candidate' => $this->when(in_array('candidate', $fields), $candidate),

            'is_applied' => (bool) ($candidate != null),
            'is_followed' => (bool) ($followed_jobs != null && in_array($this->id, $followed_jobs)),
            'is_blacklisted' => (bool) ($blacklist_companies != null
                && in_array($this->owned_id, $blacklist_companies)),
            'recalled_at' => \Arr::get($candidate, 'recalled_at') ? Carbon::createFromFormat('Y-m-d H:i:s',
                \Arr::get($candidate, 'recalled_at'))->timestamp : null,
            'is_remove_cv' => \Arr::get($candidate, 'is_remove_cv') ?? false,
            'is_viewed' => false,
            'content_arr' => $this->when(in_array('content_arr', $fields), $content_arr),
            'requirements_arr' => $this->when(in_array('requirements_arr', $fields), $requirements_arr),
            'responsibilities_arr' => $this->when(in_array('responsibilities_arr', $fields), $responsibilities_arr),

            'meta_title' => $this->when(in_array('meta_title', $fields), $this->meta_title),
            'meta_keywords' => $this->when(in_array('meta_keywords', $fields), $this->meta_keywords),
            'meta_description' => $this->when(in_array('meta_description', $fields), $this->meta_description),

            // 'announcements' => $this->when(!empty($announcement), (!empty($announcement)) ? StatusJobAnnouncementCollection::fromElasticsearch($announcement) : null),
            'announcements' => $this->when(!empty($announcement), $announcement),
            'other_supports' => $this->when(in_array('other_supports', $fields), $this->other_supports),

            // Images
            'image_thumbnail' => $this->when(in_array('image_thumbnail', $fields), $this->image_thumbnail),
            'image_content' => $this->when(in_array('image_content', $fields), $this->image_content),

            'can_edit_by_employer' => $this->when(
                in_array('can_edit_by_employer', $fields),
                $this->published_at ? Carbon::parse($this->published_at)->addDays(15)->isFuture() : true
            ),

            'employer_note' => $this->when(
                in_array('employer_note', $fields),
                (isset((static::$listDataUser)['company_id']) && !blank($this->owned_id)
                    && (static::$listDataUser)['company_id'] == $this->owned_id) ? ((string) $this->employer_note) : ''
            ),

            /*
             * Blog posts section
             */
            'blog_tags' => $this->when(in_array('blog_tags', $fields), $this->blog_tags),
            'blog_posts' => $this->when(in_array('blog_posts', $fields), $this->blog_posts),
            'sidebar_image_banner_url' => $this->when(in_array('sidebar_image_banner_url', $fields),
                \Arr::first($this->sidebar_image_banner_url)),
            'sidebar_image_link' => $this->when(in_array('sidebar_image_link', $fields), $this->sidebar_image_link),

            /**
             * Content image
             */
            'responsibilities_original' => $this->responsibilities_original,
            'requirements_original' => $this->requirements_original,
            'benefits_original' => $this->benefits_original,
            'recruitment_process_original' => $this->recruitment_process_original,
            'education_certificate' => $this->when(in_array('education_certificate', $fields), $this->education_certificate),
        ];
    }

    // private function hasLogin()
    // {
    //     return !empty(request()->user('api'));
    // }

    // private function getUserId()
    // {
    //     return request()->user('api')->id;
    // }

    private function searchCandidate($resumId, $jobId)
    {
        $candidate = (app(\Modules\Job\Repositories\Contracts\CandidateRepositoryInterface::class))
            ->searchOnElasticsearch(
                $resumId,
                [
                    'job' => $jobId,
                ]
            );
        $total = (new ElasticCollection($candidate))->total();

        return ($total > 0) ? ($candidate) : null;
    }

    // private function searchCompany($companyId, $features, $packages)
    // {
    //     $company = (app(\Modules\Company\Repositories\Contracts\CompanyRepositoryInterface::class))->searchOnElasticsearch(
    //         $companyId,
    //         [
    //             'features' => $features,
    //             'packages' => $packages
    //         ]
    //     );
    //     $total = (new ElasticCollection($company))->total();

    //     return ($total > 0) ? true : false;
    // }

    private function searchAnnouncement($id)
    {
        $announcement = app(\Modules\Announcement\Repositories\Contracts\AnnouncementInterface::class)
            ->searchOnElasticsearch(
                null,
                [
                    'model_id' => $id,
                    'model_type' => \Modules\Job\Entities\Job::class,
                    'type' => 'status_job_announcement',
                    'page' => 1,
                    'page_size' => 1,
                ]
            );

        $total = (new ElasticCollection($announcement))->total();

        return ($total > 0) ? ($announcement) : null;
    }

    private function formatSalary($salary, $locale)
    {
        if (!$this->skipInvisibleSalary()) {
            if (auth()->check() == false) {
                if ($salary['value'] == 'Negotiable') {
                    $salary['min'] = '*';
                    $salary['max'] = '*';
                    $salary['value'] = null;
                } else {
                    $salary['min'] = preg_replace('/(?<!^)\S/', '*', (string) $salary['min']);
                    $salary['max'] = preg_replace('/(?<!^)\S/', '*', (string) $salary['max']);
                    $value = strtolower(str_replace(' ', '', (string) $salary['value']));
                    if (str_contains($value, 'from')) {
                        $salary['value'] = 'From '.preg_replace('/(?<!^)\S/', '*',
                                filter_var($salary['value'], FILTER_SANITIZE_NUMBER_INT)).' '.$salary['currency'];
                    } elseif (str_contains($value, 'upto')) {
                        $salary['value'] = 'Up to '.preg_replace('/(?<!^)\S/', '*',
                                filter_var($salary['value'], FILTER_SANITIZE_NUMBER_INT)).' '.$salary['currency'];
                    } else {
                        $salary['value'] = $salary['min'].' - '.$salary['max'].' '.$salary['currency'];
                    }
                }
            }
        }

        if ($locale == 'vi_VN') {
            if ($salary['value'] == 'Negotiable') {
                $salary['value'] = 'Thương lượng';
            } else {
                $salary['value'] = str_replace(['Up to', 'From'], ['Lên tới', 'Từ'], (string) $salary['value']);
            }
        }

        return $salary;
    }

    //Hotfix thôi, chứ sau này api tự gen cái $schema_job_posting mới chịu nha
    //Schema thay đổi liên tục ko thể cứ thay đổi là phải build ES lại dc
    //Được nhưng mà nó thật sự ngu ngốc
    private function getSchemaJobPostingAttribute()
    {
        $locale = request('locale') ?? 'en_US';
        $schemaJobPosting = json_decode($this->schema_job_posting ?? '', true);

        if (!$schemaJobPosting) {
            return null;
        }

        return json_encode(array_merge($schemaJobPosting, [
            //Check locale để trả url EN or VI
            'potentialAction' => array_merge($schemaJobPosting['potentialAction'] ?? [], [
                'target' => locale_url($schemaJobPosting['potentialAction']['target'] ?? null, $locale, 'Job'),
            ]),
            'hiringOrganization' => array_merge($schemaJobPosting['hiringOrganization'], [
                'sameAs' => locale_url($schemaJobPosting['hiringOrganization']['sameAs'] ?? null, $locale, 'Company'),
            ]),
        ]));
    }

    private function skipInvisibleSalary()
    {
        return !empty(request()->header('X-Topdev-Source'))
            && in_array(request()->header('X-Topdev-Source'), $this->skipRequireLoginShowSalary ?? []);
    }

    /**
     * handle string benefit html
     *
     * @param $benefits
     *
     * @return array
     */
    public function handleBenefits($benefits)
    {
        return $benefits;
    }

    private function getRequirements()
    {
        if ($this->level == 'paid' && $this->is_content_image && $this->is_content_image_enabled) {
            return $this->requirements;
        }

        return !empty($this->requirements_original) ? $this->requirements_original : $this->requirements;
    }

    private function getResponsibilities()
    {
        if ($this->level == 'paid' && $this->is_content_image && $this->is_content_image_enabled) {
            return $this->responsibilities;
        }

        return !empty($this->responsibilities_original) ? $this->responsibilities_original : $this->responsibilities;
    }
}
