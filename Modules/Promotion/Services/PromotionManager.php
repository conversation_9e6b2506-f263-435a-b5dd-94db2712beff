<?php

namespace Modules\Promotion\Services;

use Illuminate\Support\Manager;
use Modules\Promotion\Services\Contracts\PromotionDriverContract;
use Modules\Promotion\Services\Drivers\ApplyDriver;
use Modules\Promotion\Services\Drivers\ShareFacebookDriver;

class PromotionManager extends Manager
{
    public function getDefaultDriver()
    {
        throw new \Exception('Please input the suitable driver');
    }

    public function createApplyDriver(): PromotionDriverContract
    {
        return new ApplyDriver();
    }

    public function createShareFacebookDriver(): PromotionDriverContract
    {
        return new ShareFacebookDriver();
    }
}
