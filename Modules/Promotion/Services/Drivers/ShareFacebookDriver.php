<?php

namespace Modules\Promotion\Services\Drivers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Job\Entities\Job;
use Modules\Promotion\App\Models\Promotion;
use Modules\Promotion\App\Models\PromotionReward;
use Modules\Promotion\Services\Contracts\PromotionDriverContract;

class ShareFacebookDriver implements PromotionDriverContract
{
    public const PROMOTION_ID = 2;

    private Job $job;

    public function setJob(Job $job): self
    {
        $this->job = $job;

        return $this;
    }

    public function getPromotion(): Promotion
    {
        return Promotion::query()->find(self::PROMOTION_ID);
    }

    public function hasPromotionExpired(): bool
    {
        $promotion = $this->getPromotion();

        return
            now()->lessThanOrEqualTo($promotion->started_at)
            || now()->greaterThanOrEqualTo($promotion->ended_at);
    }

    public function hasAvailableReward(): bool
    {
        return PromotionReward::query()
            ->where('promotion_id', self::PROMOTION_ID)
            ->whereNull('user_id')
            ->exists();
    }

    public function hasUserAlreadyReceivedReward(): bool
    {
        return PromotionReward::query()
            ->where('promotion_id', self::PROMOTION_ID)
            ->where('user_id', auth()->id())
            ->whereJsonContains('meta->job_id', $this->job->id)
            ->exists();
    }

    private function generateUniqueCode()
    {
        do {
            $code = str_pad(mt_rand(0, 99999), 5, '0', STR_PAD_LEFT);
        } while (PromotionReward::query()->where('promotion_id', self::PROMOTION_ID)->where('voucher_code', $code)->exists());

        return $code;
    }

    public function run(): ?PromotionReward
    {
        if ($this->hasPromotionExpired()) {
            return null;
        }

        if ($this->hasUserAlreadyReceivedReward()) {
            return null;
        }

        $voucherCode = $this->generateUniqueCode();
        $rewardedAt = now();

        return PromotionReward::create([
            'promotion_id' => self::PROMOTION_ID,
            'user_id' => auth()->id(),
            'voucher_code' => $voucherCode,
            'rewarded_at' => $rewardedAt,
            'voucher_type' => 'share-facebook',
            'meta' => [
                'job_id' => $this->job->id,
            ],
        ]);
    }
}
