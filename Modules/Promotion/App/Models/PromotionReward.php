<?php

namespace Modules\Promotion\App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Entities\User;

/**
 * PromotionReward model
 *
 * @property int $id
 * @property int $promotion_id
 * @property int|null $user_id
 * @property string|null $voucher_type
 * @property string|null $voucher_code
 * @property Carbon|null $rewarded_at
 * @property array|null $meta
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property-read Promotion $promotion
 * @method static Builder|PromotionReward isNotRewarded()
 * @method static Builder|PromotionReward isRewarded()
 * @method static Builder|PromotionReward isRewardedToUser(int $userId)
 * @method static Builder|PromotionReward isNotAssigned()
 */
class PromotionReward extends Model
{
    protected $casts = [
        'meta' => 'json',
        'rewarded_at' => 'datetime'
    ];

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'promotion_id',
        'user_id',
        'voucher_type',
        'voucher_code',
        'rewarded_at',
        'meta',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes that should be hidden for arrays.
     */
    protected $hidden = [
        'meta',
        'voucher_code',
    ];

    /**
     * Get the promotion that owns the reward.
     */
    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class);
    }

    /**
     * Scope a query to only include rewards that are not rewarded yet.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeIsNotRewarded(Builder $query): Builder
    {
        return $query->whereNull('rewarded_at');
    }

    /**
     * Scope a query to only include rewards that are already rewarded.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeIsRewarded(Builder $query): Builder
    {
        return $query->whereNotNull('rewarded_at');
    }

    /**
     * Scope a query to only include rewards that are rewarded to a specific user.
     *
     * @param  Builder  $query
     * @param  int  $userId
     *
     * @return Builder
     */
    public function scopeIsRewardedToUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include rewards that are not assigned to any user.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeIsNotAssigned(Builder $query): Builder
    {
        return $query->whereNull('user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
