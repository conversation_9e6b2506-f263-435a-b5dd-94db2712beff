<?php

namespace Modules\Promotion\App\Http\Controllers;

use App\Jobs\SystemTopDevMail;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Log;
use Modules\Job\Entities\Job;
use Modules\Promotion\App\Http\Requests\ShareFacebookApplyRequest;
use Modules\Promotion\Services\Facades\Promotion;
use Modules\Promotion\Transformers\PromotionResource;
use Modules\User\Entities\User;

class PromotionController extends Controller
{
    public function getPromotionInformation($driver)
    {
        $promotion = Promotion::driver($driver)->getPromotion();

        return new PromotionResource($promotion);
    }

    public function shareFacebookApply(ShareFacebookApplyRequest $request)
    {
        $promotionReward = $this->applyPromotion('share-facebook', Job::query()->find($request->job_id));

        if (!$promotionReward) {
            return response()->json([
                'success' => false,
                'message' => 'Apply promotion failed',
            ]);
        }

        // Send email to user
        $this->sendEmail($promotionReward);

        return response()->json([
            'success' => true,
            'message' => 'Apply promotion successfully',
            'data' => $promotionReward,
        ]);
    }

    public function shareFacebookCallback(Request $request)
    {
        $referer = request()->header('referer');
        $returnUri = request()->get('redirect_uri');
        $jobId = (int) collect(explode('-', $returnUri))->last();

        Log::info("PromotionController@shareFacebookCallback", [
            'referer' => $referer,
            'returnUri' => $returnUri,
            'jobId' => $jobId,
        ]);

        $job = Job::query()->find($jobId);

        if ($referer != 'https://m.facebook.com/' || !$returnUri || !$jobId || !$job) {
            return redirect('https://topdev.vn');
        }

        $promotionReward = $this->applyPromotion('share-facebook', $job);
        if (!$promotionReward) {
            return redirect($returnUri);
        }

        // Send email to user
        $this->sendEmail($promotionReward);

        return redirect($returnUri . '?popup-share-facebook=1');
    }

    protected function applyPromotion($driver, Job $job)
    {
        /** @var ?\Modules\Promotion\App\Models\PromotionReward $promotionReward */
        $promotionReward = Promotion::driver($driver)
            ->setJob($job)
            ->run();

        return $promotionReward;
    }

    protected function sendEmail($promotionReward)
    {
        dispatch(new SystemTopDevMail([
            'subject' => 'Chúc mừng bạn nhận được mã quay thưởng trúng Macbook',
            'firstname' => $promotionReward->user->full_name ?? $promotionReward->user->username ?? 'Ứng viên',
            'email' => $promotionReward->user->email,
            'template' => 'promotion-reward-share-facebook',
            'delay' => 0,
            'mail_type' => 'mail_to_candidate',
            
            // Haha reward
            'reward' => [
                'voucher_code' => $promotionReward->voucher_code ?? '',
            ],

            // Not sure use or not
            'facebook_url' => 'https://www.facebook.com/topdevvietnam',
            'param' => [
                '{name}' => $promotionReward->user->full_name ?? $promotionReward->user->username ?? 'Ứng viên',
                '{email}' => $promotionReward->user->email,
                '{utm}' => '?' . http_build_query([
                    'utm_source' => 'share-facebook',
                    'utm_medium' => 'email',
                    'utm_campaign' => 'share-facebook',
                ]),
            ]
        ]));
    }
}
