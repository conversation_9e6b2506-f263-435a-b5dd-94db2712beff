<?php

use Illuminate\Support\Facades\Route;
use Modules\Promotion\App\Http\Controllers\PromotionController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('promotions/share-facebook/callback', [PromotionController::class, 'shareFacebookCallback'])->name('promotions.share-facebook.callback');