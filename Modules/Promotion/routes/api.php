<?php

use Illuminate\Support\Facades\Route;
use Modules\Promotion\App\Http\Controllers\PromotionController;

/*
    |--------------------------------------------------------------------------
    | API Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register API routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
    |
*/

Route::group(['prefix' => 'promotions'], function () {
    Route::get('{driver}', [PromotionController::class, 'getPromotionInformation'])->name('promotions.driver');
    Route::post('/share-facebook/apply', [PromotionController::class, 'shareFacebookApply'])
        ->middleware('auth:api')
        ->name('promotions.share-facebook.apply');
});
