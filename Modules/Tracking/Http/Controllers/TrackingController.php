<?php

namespace Modules\Tracking\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Tracking\Entities\UserCollect;
use Illuminate\Support\Facades\Log;
use Modules\Tracking\Entities\UserCollectEvent;

class TrackingController extends Controller
{
    public function collect(Request $request)
    {
        try {
            // Get from cookie
            $sessionIds = Str::getSessionId(Cookie::get('ta'));
            $utmSource = $request->get('us');
            $utmMedium = $request->get('um');
            $utmCampaign = $request->get('uca');
            $utmContent = $request->get('uco');
            $utmTerm = $request->get('umt');

            /**
             * @var UserCollect $userCollect
             */
            $userCollect = null;
            $createNew = false;
            // If exists
            if (count($sessionIds) == 2) {
                $sessionId = $sessionIds[0];
                $sessionNumber = $sessionIds[1];
                DB::beginTransaction();
                $userCollect = UserCollect::whereSessionId($sessionId)->latest('session_number')->first();
                // Only process if client send the same number, if not does not process
                if ($userCollect) {
                    // If it has the same number with latest session number
                    if ($userCollect->session_number == $sessionNumber) {
                        // Check expired time if expired
                        if ($userCollect->timeout_at <= Carbon::now()->unix()) {
                            // Insert new with utm(source, target)
                            $userCollect = UserCollect::firstOrCreate(
                                [
                                    'session_id' => $sessionId,
                                    'session_number' => $sessionNumber + 1,
                                ],
                                [
                                    'utm_source' => $utmSource,
                                    'utm_medium' => $utmMedium,
                                    'utm_campaign' => $utmCampaign,
                                    'utm_content' => $utmContent,
                                    'utm_term' => $utmTerm,
                                ]
                            );
                        }
                    }
                } else { // Not found yet, need create new
                    $createNew = true;
                }
            } else {
                $createNew = true;
            }

            // Need create new if not found record or first time access
            if ($createNew) {
                $userCollect = UserCollect::create([
                    'session_id' => UserCollect::generateUniqueSessionId(),
                    'session_number' => 1,
                    'utm_source' => $utmSource,
                    'utm_medium' => $utmMedium,
                    'utm_campaign' => $utmCampaign,
                    'utm_content' => $utmContent,
                    'utm_term' => $utmTerm,
                ]);
            }

            $eventName = $request->get('en');
            $eventDocumentLocation = $request->get('edl');
            // Only create if allows
            if ($eventDocumentLocation && in_array($eventName, UserCollectEvent::ALLOW_TRACKING_EVENTS)) {
                UserCollectEvent::create([
                    'session_id' => $userCollect->session_id,
                    'event_group' => UserCollectEvent::getEventGroupFromName($eventName),
                    'event_name' => $eventName,
                    'event_page' => UserCollectEvent::getEventPageFromUrl($eventDocumentLocation),
                    'event_document_link' => $eventDocumentLocation,
                    'event_document_root' => $request->get('edr'),
                ]);
            }

            DB::commit();

            // Set session with session_id.session_number
            return response(null, Response::HTTP_NO_CONTENT)
                ->withCookie(cookie(
                    'ta',
                    $userCollect->session_id . '.' . $userCollect->session_number,
                    config('tracking.cookie_expired_in_minutes')
                ));
        } catch (\Exception $ex) {
            DB::rollBack();
            Log::error($ex->getMessage());
            Log::error($ex->getTraceAsString());
            app('sentry')->captureException($ex);
            return response(null, Response::HTTP_NO_CONTENT);
        }
    }
}
