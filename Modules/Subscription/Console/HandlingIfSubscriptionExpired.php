<?php

namespace Modules\Subscription\Console;

use Illuminate\Console\Command;
use Modules\Subscription\Entities\Subscription;

class HandlingIfSubscriptionExpired extends Command
{
    /**
     * @inheritdoc
     */
    protected $signature = 'subscriptions:handle-expired';

    /**
     * @inheritdoc
     */
    protected $description = 'xử lý các subscriptions hết hạn';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    public function handle(): void
    {
        $subscriptions = Subscription::whereBetween('expires_at', [
            now()->subDays(1)->startOfDay(), now()->subDays(1)->endOfDay(),
        ])->get();

        foreach ($subscriptions as $key => $subscription) {

            $subscriptionable = $subscription->subscriptionable;

            if (!empty($subscriptionable)) {

                $this->info('subscriptions ' . $subscription->taxonomy_id . ' of: ' . $subscriptionable->getKey() . ' expired');

                $package = $subscription->package;

                if (!empty($package)) {
                    if (in_array($package->slug, ['distinction', 'basic-plus'])) {
                        $subscriptionable->setAttribute('packages', [
                            ['name' => 2020],
                            ['name' => $package->term_id, 'expires_in' => $subscription->expires_at],
                        ]);
                    }
                }

                $subscriptionable->save();
            }
        }
    }
}
