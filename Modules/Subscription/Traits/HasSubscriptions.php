<?php

namespace Modules\Subscription\Traits;

use Illuminate\Database\Eloquent\Builder;
use Modules\Subscription\Entities\Feature;
use Modules\Subscription\Entities\Package;
use Modules\Subscription\Entities\Subscription;
use Modules\Taxonomy\Services\TaxonomyService;

trait HasSubscriptions
{
    /**
     * Get all of the users that are assigned this taxonomy.
     */
    public function features()
    {
        return $this->morphToMany(Feature::class, 'gables', 'term_relationships', 'gables_id', 'taxonomy_id')
                    ->using(Subscription::class)->withPivot('expires_at');
    }

    /**
     * The user may have many subscriptions.
     *
     * @return \Illuminate\Database\Eloquent\Relations\morphToMany
     */
    public function subscriptions()
    {
        return $this->morphToMany(Package::class, 'gables', 'term_relationships', 'gables_id', 'taxonomy_id')
                    ->using(Subscription::class)->withPivot('expires_at');
    }

    /**
     * Get a feature by slug.
     *
     *
     * @return Modules\Subscription\Entities\Package|null
     */
    public function feature(string $featureSlug)
    {
        return $this->features->filter(fn($feature) => $feature->isTerm($featureSlug))->first();
    }

    /**
     * Get a subscription by slug.
     *
     *
     * @return Modules\Subscription\Entities\Package|null
     */
    public function subscription(string $packageSlug)
    {
        return $this->subscriptions->filter(fn($subscription) => $subscription->isTerm($packageSlug))->first();
        // ->whereHas('term', function (Builder $builder) use ($packageSlug) {
        //     $builder->where('slug', $packageSlug);
        // })->first();
    }

    /**
     * Get all of the users that are assigned this taxonomy.
     *
     * @return bool
     */
    public function hasFeature(string $featureSlug = 'default')
    {
        $feature = $this->feature($featureSlug);

        return empty($feature) ? false : $feature->active();
    }

    /**
     * Check if the user subscribed to the given plan.
     *
     *
     * @return bool
     */
    public function subscribedTo(string $packageSlug): bool
    {
        $subscription = $this->subscription($packageSlug);

        return empty($subscription) ? false : $subscription->active();
    }

    /**
     * Get packages.
     *
     * @return array
     */
    public function getPackagesAttribute()
    {
        return $this->subscriptions->map(fn($item) => [
            'name' => $item->getKey(),
            'expires_in' => empty($item->pivot->expires_at) ? null : ($item->pivot->expires_at->isFuture() ? ($item->pivot->expires_at->diff(now())->days + 1) : 0),
        ])->toArray();
    }

    /**
     * Get packages.
     *
     * @return array
     */
    public function getServicesAttribute()
    {

        return $this->features->map(fn($item) => [
            'name' => $item->getKey(),
            'expires_in' => empty($item->pivot->expires_at) ? null : ($item->pivot->expires_at->isFuture() ? ($item->pivot->expires_at->diff(now())->days + 1) : 0),
        ])->toArray();

    }

    /**
     * Add list packages to the model.
     *
     * @param string $terms
     * @return self
     */
    public function setPackagesAttribute($terms)
    {
        $shitIds = Package::all()->pluck('id')->all();

        $collections = app(TaxonomyService::class)->prepareForSync($terms, 'packages');

        $this->subscriptions()->wherePivotIn('taxonomy_id', $shitIds)->sync($collections);

        $this->load('subscriptions');
    }

    /**
     * Add list services to the model.
     *
     * @param string $terms
     * @return self
     */
    public function setServicesAttribute($terms)
    {
        $shitIds = Feature::all()->pluck('id')->all();

        $collections = app(TaxonomyService::class)->prepareForSync($terms, 'services');

        $this->features()->wherePivotIn('taxonomy_id', $shitIds)->sync($collections);

        $this->load('features');

    }

    /**
     * Get next plans.
     *
     * @return array
     */
    public function nextPlans()
    {
        return (array) ($this->planWhenExpired ?? null);
    }
}
