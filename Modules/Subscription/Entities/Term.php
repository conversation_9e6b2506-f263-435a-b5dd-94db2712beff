<?php

namespace Modules\Subscription\Entities;

use Illuminate\Database\Eloquent\Model;

class Term extends Model
{
    /**
     * @inheritdoc
     */
    protected $table = 'terms';

    /**
     * @inheritdoc
     */
    protected static function boot()
    {
        parent::boot();
    }

    /**
     * Get packages.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function packages()
    {
        return $this->hasMany(Package::class, 'term_id', 'id');
    }

    /**
     * Get features.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function features()
    {
        return $this->hasMany(Feature::class, 'term_id', 'id');
    }
}
