<?php

namespace Modules\Subscription\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    public const TAXONOMY_NAME = 'services';
    public const PACKAGE_FREE = 3904;
    public const PACKAGE_BASIC = 3908;
    public const PACKAGE_BASIC_PLUS = 3910;
    public const PACKAGE_DISTINCTION = 3912;

    use AttributeTrait;

    /**
     * @inheritdoc
     */
    protected $cachePrefix = 'package-prefix';

    /**
     * @inheritdoc
     */
    protected $cacheCooldownSeconds = 5;

    /**
     * @inheritdoc
     */
    protected $table = 'term_taxonomy';

    /**
     * @inheritdoc
     */
    protected $primaryKey = 'id';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'id',
        'term_id',
        'taxonomy',
        'parent',
    ];

    /**
     * @inheritdoc
     */
    public $timestamps = false;

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('packages', function (Builder $builder) {
            $builder->where('taxonomy', 'packages');
        });
    }

    /**
     * Get term.
     *
     * @return \Illuminate\Database\Eloquent\Relations\belongsTo
     */
    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    /**
     * Get the parent taxonomy.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent');
    }

    /**
     * Get the children taxonomies.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrens()
    {
        return $this->hasMany(self::class, 'parent');
    }

    /**
     * Get all of the users that are assigned this taxonomy.
     */
    public function features()
    {
        return $this->morphedByMany(Feature::class, 'gables', 'term_relationships', 'taxonomy_id', 'gables_id')
                ->withPivot('expires_at');
    }

    /**
     * Check term by slug.
     *
     * @return bool
     */
    public function isTerm($slug = 'default')
    {
        if (is_null($this->term)) {
            return false;
        }

        return $this->slug == $slug;
    }

    /**
     * Get all of the users that are assigned this taxonomy.
     *
     * @return bool
     */
    public function hasFeature($slug = 'default')
    {
        return $this->features()->filter(fn($feature) => $feature->slug == $slug)->isNotEmpty();
    }

    /**
     * Check if subscription is active.
     *
     * @return bool
     */
    public function active()
    {
        return $this->onGracePeriod() || $this->isEternal();
    }

    /**
     * Determine if the subscription is within its grace period after cancellation.
     *
     * @return bool
     */
    public function onGracePeriod()
    {
        return $this->expires_at && $this->expires_at->isFuture();
    }

    /**
     * Check if the model is eternal.
     *
     * @return bool
     */
    public function isEternal()
    {
        return is_null($this->expires_at);
    }

    /**
     * Get attribute "expires_at".
     *
     * @return \Carbon\Carbon|null
     */
    public function getExpiresAtAttribute()
    {
        if (empty($this->pivot)) {
            return null;
        }

        return $this->pivot->expires_at;
    }

    /**
     * ‎ﺩ／|、
     * (ﾟ､ 。7.
     */
    public static function getScoreByPackage($id)
    {
        $list = static::getScore();

        return $list[$id] ?? 0;
    }

    /**
     * Get score for the package.
     *
     * @return array
     */
    public static function getScore()
    {
        return [
            static::PACKAGE_FREE => 1,
            static::PACKAGE_BASIC => 2,
            static::PACKAGE_BASIC_PLUS => 3,
            static::PACKAGE_DISTINCTION => 4,
        ];
    }
}
