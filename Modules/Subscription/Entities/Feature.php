<?php

namespace Modules\Subscription\Entities;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Feature extends Model
{
    use AttributeTrait;

    /**
     * @inheritdoc
     */
    protected $table = 'term_taxonomy';

    /**
     * @inheritdoc
     */
    protected $primaryKey = 'id';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'id',
        'term_id',
        'taxonomy',
        'parent',
    ];

    /**
     * @inheritdoc
     */
    public $timestamps = false;

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('services', function (Builder $builder) {
            $builder->where('taxonomy', 'services');
        });
    }

    /**
     * Get term.
     *
     * @return \Illuminate\Database\Eloquent\Relations\belongsTo
     */
    public function term()
    {
        return $this->belongsTo(Term::class, 'term_id', 'id');
    }

    /**
     * Check term by slug.
     *
     * @return bool
     */
    public function isTerm($slug = 'default')
    {
        if (is_null($this->term)) {

            return false;
        }

        return $this->term->slug == $slug;
    }

    /**
     * Check if subscription is active.
     *
     * @return bool
     */
    public function active()
    {
        return $this->onGracePeriod() || $this->isEternal();
    }

    /**
     * Determine if the subscription is within its grace period after cancellation.
     *
     * @return bool
     */
    public function onGracePeriod()
    {
        return $this->expires_at && $this->expires_at->isFuture();
    }

    /**
     * Check if the model is eternal.
     *
     * @return bool
     */
    public function isEternal()
    {
        return is_null($this->expires_at);
    }

    /**
     * Get attribute "expires_at".
     *
     * @return \Carbon\Carbon|null
     */
    public function getExpiresAtAttribute()
    {
        if (empty($this->pivot)) {
            return null;
        }

        return $this->pivot->expires_at;
    }
}
