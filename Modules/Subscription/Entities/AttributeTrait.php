<?php

namespace Modules\Subscription\Entities;

trait AttributeTrait
{
    /**
     * Get name.
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return $this->term->name;
    }

    /**
     * Get slug.
     *
     * @return string
     */
    public function getSlugAttribute()
    {
        return $this->term->slug;
    }

    /**
     * Get feature.
     *
     * @return string
     */
    public function getFeatureAttribute()
    {
        return $this->term->feature;
    }

    /**
     * Get description.
     *
     * @return string
     */
    public function getDescriptionAttribute()
    {
        return $this->term->description;
    }
}
