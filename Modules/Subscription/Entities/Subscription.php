<?php

namespace Modules\Subscription\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphPivot;
use Illuminate\Support\Collection;

class Subscription extends MorphPivot
{
    /**
     * @inheritdoc
     */
    protected $table = 'term_relationships';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'taxonomy_id',
        'gables_id',
        'gables_type',
        'expires_at',
    ];

    /**
     * @inheritdoc
     */
    protected $casts = [
        'expires_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * @inheritdoc
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function subscriptionable()
    {
        return $this->morphTo('gables');
    }

    /**
     * Get the term's owner.
     *
     * @return \App\Traits\customRelation
     */
    public function term()
    {

        return $this->customRelation(
            Term::class,

            // add constraints
            function ($relation) {

                $relation
                    ->getQuery()
                    ->join('term_taxonomy', function ($join) {
                        $join->on('term_taxonomy.term_id', '=', 'terms.id');

                        if ($this->getKey()) {
                            $join->where('term_taxonomy.id', $this->taxonomy_id);
                        }
                    });
            },

            // add eager constraints
            function ($relation, $subscriptions) {

                $relation
                    ->getQuery()
                    ->whereIn('term_taxonomy.id', collect($subscriptions)->pluck('taxonomy_id')->all())
                    ->with('packages', 'features');
            },
            function (array $subscriptions, Collection $results, $relation, $customRelation) {
                if ($results->isEmpty()) {
                    return $subscriptions;
                }

                foreach ($subscriptions as $subscription) {
                    $subscription->setRelation(
                        $relation,
                        $results->filter(fn(Term $term) => $term->packages->contains($subscription->taxonomy_id) || $term->features->contains($subscription->taxonomy_id))->first()
                    );
                }

                return $subscriptions;
            },
            false
        );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function feature()
    {
        return $this->belongsTo(Feature::class, 'taxonomy_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function package()
    {
        return $this->belongsTo(Package::class, 'taxonomy_id');
    }

    /**
     * Xử lý kế hoạch tiếp theo của subscriptionable, có thể chuyển sang 1 gói khác.
     *
     * @return void
     */
    public function handleNextPlans()
    {
        if (method_exists($this->subscriptionable, 'nextPlans')) {
            $plans = $this->subscriptionable->nextPlans();

            $plan = $plans;
        }
    }
}
