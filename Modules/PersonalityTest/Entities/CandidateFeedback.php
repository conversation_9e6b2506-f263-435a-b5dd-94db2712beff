<?php

namespace Modules\PersonalityTest\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Modules\User\Entities\User;

/**
 * Modules\PersonalityTest\Entities\CandidateFeedback.
 *
 * @property int $id
 * @property int $user_id
 * @property int|null $candidate_id
 * @property int|null $is_visible
 * @property int|null $is_share
 * @property string|null $suitable
 * @property string|null $content
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $suitable_name
 * @property-read User $user
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback query()
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereCandidateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereIsShare($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereIsVisible($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereSuitable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CandidateFeedback whereUserId($value)
 * @mixin \Eloquent
 */
class CandidateFeedback extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_personality_test';

    public const COMPLETELY_INCORRECT = 1;
    public const INCORRECT = 2;
    public const NORMAL = 3;
    public const ALMOST_EXACTLY = 4;
    public const EXACTLY = 5;

    public const suitables = [
        self::COMPLETELY_INCORRECT => 'Hoàn toàn không đúng',
        self::INCORRECT => 'Không đúng',
        self::NORMAL => 'Bình thường',
        self::ALMOST_EXACTLY => 'Có vẻ đúng',
        self::EXACTLY => 'Hoàn toàn đúng',
    ];

    protected $table = 'candidate_feedbacks';
    protected $timeStamps = true;

    protected $fillable = [
        'id',
        'user_id',
        'candidate_id',
        'is_visible',
        'is_share',
        'suitable',
        'content',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getSuitableNameAttribute()
    {
        if (!$this->suitable) {
            return '';
        }

        return Arr::get(self::suitables, $this->suitable);
    }
}
