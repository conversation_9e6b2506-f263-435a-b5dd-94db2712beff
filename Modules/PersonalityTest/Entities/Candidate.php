<?php

namespace Modules\PersonalityTest\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\PersonalityTest\Jobs\ResetCandidateFromSaramin;

/**
 * Modules\PersonalityTest\Entities\Candidate.
 *
 * @property int $id
 * @property int $user_id
 * @property int $test_id
 * @property int|null $status
 * @property int|null $is_final_test
 * @property string|null $created_device
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $saramin_deleted_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Modules\PersonalityTest\Entities\CandidateFeedback|null $feedback
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate newQuery()
 * @method static \Illuminate\Database\Query\Builder|Candidate onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate query()
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereCreatedDevice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereIsFinalTest($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereSaraminDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereTestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Candidate whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|Candidate withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Candidate withoutTrashed()
 * @mixin \Eloquent
 */
class Candidate extends Model
{
    use SoftDeletes;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_personality_test';

    public const FINAL_TEST = 1;
    public const WAITING_STATUS = 0;
    public const TAKING_STATUS = 1;
    public const COMPLETED_WITHOUT_GRADING_STATUS = 2;
    public const TIMEOUT_STATUS = 3;
    public const COMPLETED_WITH_GRADING_STATUS = 12;
    public const COMPLETED_WITH_NO_ANSWERS = 13;

    protected $fillable = [
        'id',
        'user_id',
        'test_id',
        'status',
        'is_final_test',
        'completed_at',
        'created_device',
        'saramin_deleted_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'completed_at' => 'datetime',
        'saramin_deleted_at' => 'datetime',
    ];

    protected $attributes = [
        'is_final_test' => self::FINAL_TEST,
    ];

    public static function boot()
    {
        parent::boot();

        static::deleted(function (self $model) {
            if (!$model->isSaraminDeleted()) {
                $model->resetFromSaraminKorean();
            }
            if (!method_exists($model, 'runSoftDelete') || $model->isForceDeleting()) {
                $model->feedback()->forceDelete();
            }
        });
    }

    public $incrementing = false;

    public function isFinalTest()
    {
        return (bool) $this->is_final_test;
    }

    /**
     * Check if the test is completed.
     *
     * @return bool
     */
    public function isCompleted()
    {
        return !is_null($this->completed_at);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Check if the test was created in the same day of user's first login.
     *
     * @return bool
     */
    public function isFirstLoginAndStartTest()
    {
        return $this->created_at->isSameDay(optional($this->user)->first_login_at);
    }

    public function feedback()
    {
        return $this->hasOne(CandidateFeedback::class, 'candidate_id');
    }

    public function isSaraminDeleted()
    {
        return !empty($this->saramin_deleted_at);
    }

    public function markCandidateAsDeletedFromSaramin()
    {
        return $this->forceFill([
            'saramin_deleted_at' => $this->freshTimestamp(),
        ])->save();
    }

    public function resetFromSaraminKorean()
    {
        ResetCandidateFromSaramin::dispatch($this);
    }
}
