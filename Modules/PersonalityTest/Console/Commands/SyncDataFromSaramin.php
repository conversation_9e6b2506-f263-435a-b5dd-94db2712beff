<?php

namespace Modules\PersonalityTest\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Modules\PersonalityTest\Entities\Candidate;
use Modules\PersonalityTest\Jobs\ProcessStatus;

class SyncDataFromSaramin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'candidates:sync {date}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $date = Carbon::parse($this->argument('date'))->format('Y-m-d H:i:s');
        $now = Carbon::now()->format('Y-m-d H:i:s');

        $data = Candidate::whereBetween('created_at', [$date, $now])
            ->chunk(1000, function ($candidates) {
                foreach ($candidates as $candidate) {
                    ProcessStatus::dispatch($candidate);
                }
            });
    }
}
