<?php

namespace Modules\PersonalityTest\Console\Commands;

use App\Jobs\SystemTopDevMail;
use Illuminate\Console\Command;
use Modules\PersonalityTest\Entities\Candidate;

class EmailRemindPersonalityTest extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ptest:send-remind-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send remind email to candidate who not complete personality test';

    protected array $utm = [
        'utm_source' => 'remind_ptest',
        'utm_medium' => 'email',
        'utm_campaign' => 'automation',
    ];

    protected string $subJect = '[TopDev] Có vẻ bạn chưa hoàn thành bài Trắc nghiệm tính cách. Giải mã ngay nhé!';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \Log::info('----EmailRemindPersonalityTest');
        Candidate::whereIn('candidates.id', function ($query) {
            $query->from('candidates')
                ->select('candidates.id')
                ->rightJoin(
                    \DB::raw(
                        '
                        (
                            SELECT candidates.user_id, MAX(candidates.created_at) AS max
                            FROM candidates
                            GROUP BY candidates.user_id
                        ) sub
                    '
                    ),
                    function ($join) {
                        $join->on('candidates.user_id', '=', 'sub.user_id');
                        $join->on('candidates.created_at', '=', 'sub.max');
                    }
                );
        })
            ->where(\DB::raw('DATEDIFF(now(), created_at)'), 3)
            ->whereNotIn('status', [
                Candidate::COMPLETED_WITHOUT_GRADING_STATUS,
                Candidate::COMPLETED_WITH_GRADING_STATUS,
                Candidate::COMPLETED_WITH_NO_ANSWERS,
            ])
            ->with('user')
            ->chunk(1000, function ($candidates) {
                foreach ($candidates as $candidate) {
                    \Log::info('EmailRemindPersonalityTest email: ' . $candidate->user->email);
                    try {
                        $payload = [
                            'firstname' => (isset($candidate->user->full_name) && !empty($candidate->user->full_name)) ? $candidate->user->full_name : $candidate->user->username,
                            'email' => $candidate->user->email,
                            'subject' => $this->subJect,
                            'template' => 'remind_ptest',
                            'delay' => 0,
                            'mail_type' => 'autoflow',
                            'param' => [
                                '{name}' => (isset($candidate->user->full_name) && !empty($candidate->user->full_name)) ? $candidate->user->full_name : $candidate->user->username,
                                '{email}' => $candidate->user->email,
                                '{utm}' => '?' . http_build_query($this->utm),
                            ],
                            'test_id' => $candidate->id,
                        ];

                        dispatch(new SystemTopDevMail($payload));
                    } catch (\Exception) {
                        \Log::error('ERROR EmailRemindPersonalityTest :' . $candidate->id);
                    }
                }
            });
    }
}
