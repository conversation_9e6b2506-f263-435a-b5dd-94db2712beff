<?php

namespace Modules\PersonalityTest\Console\Commands;

use Google\Client;
use Google\Service\Sheets;
use Google\Service\Sheets\ClearValuesRequest;
use Google\Service\Sheets\ValueRange;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\PersonalityTest\Entities\Candidate;

class ExportCandidateSheets extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'google:export_candidate_sheets';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected $chunkCount = 1000;

    /**
     * Google Service for Sheets.
     *
     * @var Google_Service_Sheets
     */
    protected $googleService;
    protected $sheetId;
    protected $sheetName;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();

        $this->sheetId = config('google.sheet_id');
        $this->sheetName = config('google.sheet_name');
    }

    public function getGoogleClient()
    {
        $client = new Client();
        $client->setAuthConfig(storage_path('gg_credentials/credentials.json'));
        $client->setScopes(Sheets::SPREADSHEETS);

        return $client;
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info('-------ExportCandidateSheets-------');
        Log::info('-------ExportCandidateSheets-------');

        $this->googleService = new Sheets($this->getGoogleClient());

        $this->clearSheet();
        $this->appendTitleToSheet();

        Candidate::with(['user', 'feedback', 'user.firstAuthenticationLog'])
            ->chunk($this->chunkCount, function ($candidates) {
                $data = [];

                foreach ($candidates as $candidate) {
                    $data[] = [
                        optional($candidate->user)->id ?? '',
                        optional($candidate->user)->email ?? '',
                        $candidate->created_at->toDateTimeString(),
                        $candidate->isCompleted() ? $candidate->completed_at->toDateTimeString() : '',
                        $candidate->isFirstLoginAndStartTest() ? 'x' : '',

                        $candidate->feedback ? $candidate->feedback->candidate_id : '',
                        $candidate->feedback ? $candidate->feedback->created_at->toDateTimeString() : '',
                        $candidate->feedback ? $candidate->feedback->suitable_name : '',
                        $candidate->feedback && $candidate->feedback->content != null ? $candidate->feedback->content : '',
                        $candidate->feedback && $candidate->feedback->is_share === 1 ? 'x' : '',
                        $candidate->feedback && $candidate->feedback->is_visible === 1 ? 'x' : '',
                        $candidate->created_device,
                    ];
                }

                $this->appendDataToSheet($data);
            });

        $this->info('Export Success');
        Log::info('-----ExportCandidateSheets Success Update Google---------');
    }

    protected function appendDataToSheet($data)
    {
        $this->googleService->spreadsheets_values->append(
            $this->sheetId,
            $this->sheetName,
            new ValueRange([
                'values' => $data,
            ]),
            ['valueInputOption' => 'USER_ENTERED']
        );
    }

    protected function appendTitleToSheet()
    {
        $this->appendDataToSheet([
            [
                'User Id',
                'Email',
                'Created At',
                'Completed At',
                '1st Login',

                'Candidate Id',
                'Submitted At',
                'Question 1',
                'Question 2',
                'Is Share',
                'Is Visible',
                'Device',
            ],
        ]);
    }

    protected function clearSheet()
    {
        $this->googleService->spreadsheets_values->clear(
            $this->sheetId,
            $this->sheetName,
            new ClearValuesRequest()
        );
    }
}
