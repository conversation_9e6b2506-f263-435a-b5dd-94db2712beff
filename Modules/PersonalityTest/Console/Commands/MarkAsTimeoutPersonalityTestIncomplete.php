<?php

namespace Modules\PersonalityTest\Console\Commands;

use Illuminate\Console\Command;
use Modules\PersonalityTest\Entities\Candidate;

class MarkAsTimeoutPersonalityTestIncomplete extends Command
{
    public const MAXIMUM_DAYS_TO_COMPLETE_PT = 7;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tests:reset';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command reset';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Candidate::where('id', '>', 0)->update([
            'is_final_test' => 0,
        ]);

        return Command::SUCCESS;
    }
}
