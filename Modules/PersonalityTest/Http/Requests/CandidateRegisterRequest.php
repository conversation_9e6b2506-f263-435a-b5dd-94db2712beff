<?php

namespace Modules\PersonalityTest\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class CandidateRegisterRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'exam_id' => 'required',
            'source' => 'required|in:mobile,pc',
        ];
    }

    public function device()
    {
        $agent = new Agent();
        $device = 'PC';

        if ($agent->isMobile() || $agent->isMobileBot()) {
            $device = 'MobileWeb';
        }

        return $this->header('X-Topdev-Source') == 'MobileApp' ? 'MobileApp' : $device;
    }
}
