<?php

namespace Modules\PersonalityTest\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FeedbackRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_visible' => 'required_without_all:is_share,suitable|in:0,1',
            'is_share' => 'required_without_all:is_visible,suitable|in:0,1',
            'suitable' => 'required_without_all:is_share,is_visible|in:1,2,3,4,5',
        ];
    }
}
