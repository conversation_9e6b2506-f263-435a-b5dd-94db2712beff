<?php

namespace Modules\PersonalityTest\Http\Controllers;

use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Modules\PersonalityTest\Entities\Candidate;
use Modules\PersonalityTest\Entities\CandidateFeedback;
use Modules\PersonalityTest\Helpers\Helper;
use Modules\PersonalityTest\Http\Requests\CandidateRegisterRequest;
use Modules\PersonalityTest\Http\Requests\FeedbackRequest;
use Modules\PersonalityTest\Http\Requests\MultiAnswerExamRequest;
use Modules\PersonalityTest\Http\Requests\StartExamRequest;
use Modules\PersonalityTest\Jobs\ProcessStatus;
use Modules\User\Entities\User;

class CandidateController extends SaraminController
{
    protected $svqExamIdInfo = [
        'aptitude_sri' => 107,
        'personality_sri' => 177,
    ];

    /**
     * @throws GuzzleException
     */
    public function getAllExam()
    {
        $userId = Auth::id();
        $apiUrl = Helper::getApiUrl('users/{user_id}/available-exam', ['user_id' => $userId]);
        $response = Helper::callApi('get', $apiUrl);
        $candidates = Candidate::query()
            ->where(['user_id' => $userId])
            ->orderByDesc('created_at')
            ->get();

        $isFinalTest = empty($candidates->first()) ? false : $candidates->first()->isFinalTest();
        $response['data']['exam']['is_final_test'] = $isFinalTest;

        foreach ($response['data']['apply'] as $key => $apply) {
            if (!$candidates->contains($apply['apl_id'])) {
                unset($response['data']['apply'][$key]);
                continue;
            }

            $response['data']['apply'][$key]['cre_dt'] = Carbon::createFromFormat('Y-m-d H:i:s', $apply['cre_dt'], 'Asia/Seoul')->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s');
            $response['data']['apply'][$key]['mod_dt'] = Carbon::createFromFormat('Y-m-d H:i:s', $apply['mod_dt'], 'Asia/Seoul')->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s');
            $response['data']['apply'][$key]['end_dt'] = Carbon::createFromFormat('Y-m-d H:i:s', $apply['cre_dt'], 'Asia/Seoul')->addDays(7)->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s');
            $response['data']['apply'][$key]['exam_nm_vi'] = '[' . Carbon::parse($response['data']['apply'][$key]['cre_dt'])->format('F') . '] Workplace Personality Test';
        }

        $response['data']['apply'] = array_values($response['data']['apply']);

        return $response;
    }

    /**
     * @throws GuzzleException
     */
    public function candidateRegister(CandidateRegisterRequest $request)
    {
        $user = $request->user();
        $user = User::query()->find($user->getKey());

        $apiUrl = Helper::getApiUrl('users/{user_id}', ['user_id' => $user->getKey()]);
        $latestTest = Candidate::query()
            ->where('user_id', $user->getKey())
            ->orderByDesc('created_at')
            ->first();

        if ($latestTest && $latestTest->isFinalTest()) {
            return response()->json([
                'success' => false,
                'message' => 'You have reached max quotas!',
            ], 400);
        }

        $params = [
            'user_id' => $user->getKey(),
            'exam_id' => $request->input('exam_id'),
            'user_name' => empty($user->display_name) ? (
                empty($user->fullname) ? $user->username : $user->fullname
            ) : $user->display_name,
            'source' => $request->input('source'),
        ];

        $response = Helper::callApi('post', $apiUrl, $params);

        if (isset($response['data']['apl_id'])) {
            $cadidate = [
                'id' => $response['data']['apl_id'],
                'user_id' => $user->getKey(),
                'test_id' => $request->input('exam_id'),
                'status' => Candidate::TAKING_STATUS,
                'is_final_test' => Candidate::FINAL_TEST,
                'created_device' => $request->device(),
            ];

            Candidate::query()->updateOrCreate(
                ['id' => $response['data']['apl_id'], 'user_id' => $user->getKey()],
                $cadidate
            );

            return response()->json([
                'status' => 200,
                'message' => '',
                'data' => $cadidate,
            ]);
        }

        return $response;
    }

    /**
     * @throws GuzzleException
     */
    public function getExamByUser(Candidate $candidate, Request $request)
    {
        $page = $request->page ?? 1;
        $limit = $request->page_size ?? Helper::PAGE_LIMIT;

        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/exam?type=combine', ['candidate_id' => $candidate->getKey()]);

        $res = Helper::callApi('get', $apiUrl);

        if (empty($res['data'])) {
            return $res;
        }

        $new = array_filter($res['data']['question'], fn($data) => $data['qus_dir_tp'] == 0);

        foreach ($new as $key => $question) {
            if (empty($question['answer'])) {
                $new[$key]['answer'] = null;
            }
        }

        $data = $this->paginate($new, $limit, $page);

        $res['data']['question'] = $data['data'];
        $res['data']['links'] = $data['links'];
        $res['data']['meta'] = $data['meta'];

        return response()->json($res);
    }

    /**
     * @throws GuzzleException
     */
    public function startExam(Candidate $candidate, StartExamRequest $request)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/exam', [
            'candidate_id' => $candidate->getKey(),
        ]);

        return Helper::callApi('post', $apiUrl, [
            'type' => $request->input('type'),
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function updateExamTime(Candidate $candidate)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/use-second', [
            'candidate_id' => $candidate->getKey(),
        ]);

        $params = [
            'type' => 'update',
        ];

        return Helper::callApi('post', $apiUrl, $params);
    }

    /**
     * @throws GuzzleException
     */
    public function checkremainTime(Candidate $candidate)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/use-second', ['candidate_id' => $candidate->getKey()]);

        return Helper::callApi('get', $apiUrl);
    }

    /**
     * @throws GuzzleException
     */
    public function answerExam(Candidate $candidate, Request $request)
    {
        $questionId = $request->input('question_id');
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/answer/{question_id}', [
            'candidate_id' => $candidate->getKey(),
            'question_id' => $questionId,
        ]);

        $params = [
            'answer_type' => $request->answer_type ?? 1,
            'answer' => $request->answer ?? 1,
        ];

        ProcessStatus::dispatch($candidate);

        return Helper::callApi('post', $apiUrl, $params);
    }

    /**
     * @throws GuzzleException
     */
    public function multiAnswerExam(Candidate $candidate, MultiAnswerExamRequest $request)
    {
        $answerJson = $request->input('answer_json');
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/answer-json', [
            'candidate_id' => $candidate->getKey(),
        ]);

        $params = [
            'answer_json' => $answerJson,
        ];

        ProcessStatus::dispatch($candidate);

        return Helper::callApi('post', $apiUrl, $params);
    }

    /**
     * @throws GuzzleException
     */
    public function markResult(Candidate $candidate, Request $request)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/mark', [
            'candidate_id' => $candidate->getKey(),
        ]);

        ProcessStatus::dispatch($candidate);

        return Helper::callApi('post', $apiUrl, $request->all());
    }

    /**
     * @throws GuzzleException
     */
    public function getResultExam(Candidate $candidate)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/result', ['candidate_id' => $candidate->getKey()]);
        $data = Helper::callApi('get', $apiUrl);
        $resultData = json_decode((string) ($data['data']['rslt_json'] ?? null), true);

        // $data = file_get_contents('data.json');
        // $resultData = json_decode($data ?? null, true);
        $totalReportData = $this->getVNTotalReportData($resultData);
        $applyInfo = $this->getReportApplyInfo($resultData);
        $personsReport = $this->getVNReportInfo($totalReportData);
        $reliabilityResultInfo = $this->getVNReliabilityResultInfo($totalReportData);

        return response()->json([
            'applyInfo' => array_merge($applyInfo, [
                'examEndDt' => Carbon::createFromFormat('Y-m-d H:i:s', $data['data']['mod_dt'], 'Asia/Seoul')->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s'),
            ]),
            'feedback' => $candidate->feedback ?? null,
            'personsInfo' => !empty($personsReport['dataInfo']) ? $personsReport['dataInfo'] : [],
            'personsChildInfo' => !empty($personsReport['childDataInfo']) ? $personsReport['childDataInfo'] : [],
            'personsRadarData' => !empty($personsReport['childData']) ? ($this->setVNRadarData($personsReport['childData'])) : [],
            'personsRadarDataJson' => json_encode(!empty($personsReport['childData']) ? ($this->setVNRadarData($personsReport['childData'])) : []),
            'reliabilityResultInfo' => $reliabilityResultInfo,
        ]);
    }

    // public function getResultExams(Candidate $candidate, Request $request)
    // {
    //     $apiUrl = Helper::getApiUrl('apply/{candidate_id}/result', ['candidate_id' => $candidate->getKey()]);
    //     $data = Helper::callApi('get', $apiUrl)->json();
    //     $resultData = json_decode($data['data']['rslt_json'] ?? null, true);
    //     // $data = file_get_contents('data.json');
    //     // $resultData = json_decode($data ?? null, true);

    //     $applyInfo = $this->getReportApplyInfo($resultData);
    //     $totalReportData = $this->getTotalReportData($resultData);
    //     $personsReport = $this->getPersonsReport($totalReportData);
    //     $reliabilityResultInfo = $this->getReliabilityResultInfo($totalReportData);

    //     return response()->json([
    //         'applyInfo' => $applyInfo,
    //         'personsInfo' => !empty($personsReport['dataInfo']) ? $personsReport['dataInfo'] : [],
    //         'personsChildInfo' => !empty($personsReport['childDataInfo']) ? $personsReport['childDataInfo'] : [],
    //         'personsRadarData' => !empty($personsReport['childData']) ? ($this->setRadarData($personsReport['childData'])) : [],
    //         'reliabilityResultInfo' => $reliabilityResultInfo
    //     ]);

    // }

    /**
     * @throws GuzzleException
     */
    public function getCandidate(Candidate $candidate)
    {
        $apiUrl = Helper::getApiUrl('apply/{candidate_id}', ['candidate_id' => $candidate->getKey()]);

        return Helper::callApi('get', $apiUrl);
    }

    private function paginate($items, $perPage = 5, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);

        $data = (new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options))->toArray();

        return [
            'data' => array_values($data['data']),
            'links' => [
                'first' => null,
                'last' => null,
                'prev' => $data['prev_page_url'] ? url()->current() . $data['prev_page_url'] : null,
                'next' => $data['next_page_url'] ? url()->current() . $data['next_page_url'] : null,
            ],
            'meta' => [
                'total' => $data['total'],
                'per_page' => $data['per_page'],
                'current_page' => $data['current_page'],
                'last_page' => $data['last_page'],
                'from' => $data['from'],
                'to' => $data['to'],
            ],
        ];
    }

    public function feedback(Candidate $candidate, FeedbackRequest $request)
    {
        $feedback = CandidateFeedback::query()->updateOrCreate(
            [
                'user_id' => Auth::user()->id,
                'candidate_id' => $candidate->getKey(),
            ],
            [
                'is_visible' => $request->input('is_visible'),
                'is_share' => $request->input('is_share'),
                'suitable' => $request->input('suitable'),
                'content' => $request->input('content'),
            ],
        );

        return response()->json([
            'status' => 200,
            'message' => '',
            'data' => $feedback,
        ]);
    }
}
