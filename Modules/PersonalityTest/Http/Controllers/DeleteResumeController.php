<?php

namespace Modules\PersonalityTest\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\PersonalityTest\Entities\Candidate;

class DeleteResumeController extends Controller
{
    public function destroy($id)
    {
        Candidate::where('user_id', $id ?? -99999)->get()
                ->each->delete();

        return response([
            'message' => 'success',
        ]);
    }
}
