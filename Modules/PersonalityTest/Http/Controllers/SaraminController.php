<?php

namespace Modules\PersonalityTest\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\PersonalityTest\Helpers\Code;

abstract class SaraminController extends Controller
{
    /**
     * 응답 신뢰도 데이터 가져오기
     * 앞써 가져온 json 데이터에서 응답 신뢰도 데이터만 만들기.
     * @param array $totalReportData
     * @param string $siatSvqCategory
     * @return array
     */
    protected function getReliabilityResultInfo($totalReportData = [], $siatSvqCategory = 'personality_sri')
    {
        if ($siatSvqCategory == 'personality_sri') {
            return $this->getResultBasicInfo($totalReportData['childScaleList'][Code::SCALE_CODE['paid_persons_totrlbt']]['childScaleList'][Code::SCALE_CODE['paid_persons_totrlbt_reliability']] ?? []);
        }

        return $this->getResultBasicInfo($totalReportData['childScaleList'][Code::SCALE_CODE['combination_totrlbt']]['childScaleList'][Code::SCALE_CODE['combination_totrlbt_reliability']] ?? []);
    }

    /**
     * 직무 적합도 데이터 만들기
     * 앞써 가져온 json 데이터에서 직무 적합도 척도 데이터만 만들기.
     * @param array $resultData
     * @param string $siatSvqCategory
     * @return array
     */
    protected function getTotaljobInfo($resultData = [], $siatSvqCategory = 'personality_sri')
    {
        // 유료 인성
        if ($siatSvqCategory == 'personality_sri') {
            return $this->getResultBasicInfo($resultData['result'][2]['data'][394], 'pecentileScore');

        }

        return $this->getResultBasicInfo(
            $resultData['result'][Code::REPORT_RESULT_CODE['combination_job']]['data'][Code::SCALE_CODE['combination_job']] ?? [],
            'pecentileScore'
        );
    }

    /**
     * 인성 데이터 가져오기
     * 앞써 가져온 결과 json data에서 인성 척도 데이터만 가져온다.
     * @param array $totalReportData
     * @param string $siatSvqCategory
     * @return array
     */
    protected function getPersonsReport($totalReportData = [], $siatSvqCategory = 'personality_sri')
    {
        // 유료, 무료에 따라 가져오는 척도 번호가 다른데 앱랜서는 무료만 있으니 척도 번호를 고정해서 하나만 가져오면 된다.
        if ($siatSvqCategory == 'personality_sri') {
            return $this->refineReportData($totalReportData, Code::SCALE_CODE['paid_persons'], Code::SCALE_CODE['paid_persons_sixtotal']);
        }

        return $this->refineReportData($totalReportData, Code::SCALE_CODE['combination_persons'], Code::SCALE_CODE['combination_persons_sixtotal']);
    }

    /**
     * 종합 데이터 가져오기
     * 앞써 가져온 결과 json에서 종합 척도 데이터만 추출하기
     * 인성과 적성 종합척도 번호가 다름.
     * @param array $resultData
     * @param string $siatSvqCategory
     * @return mixed
     */
    protected function getTotalReportData($resultData = [], $siatSvqCategory = 'personality_sri')
    {
        // 인성
        if ($siatSvqCategory == 'personality_sri') {
            return $resultData['result'][Code::REPORT_RESULT_CODE['persons']]['data'][Code::SCALE_CODE['paid_persons_total']] ?? [];
        }

        // 적성
        return $resultData['result'][Code::REPORT_RESULT_CODE['combination']]['data'][Code::SCALE_CODE['combination']] ?? [];
    }

    /**
     * 특정 척도 결과 데이터 만들기
     * 결과지에서 사용할 데이터 형태로 만든다.
     * @param array $totalReportData
     * @param int $parentCode
     * @param int $childCode
     * @return array
     */
    protected function refineReportData($totalReportData, $parentCode, $childCode)
    {
        // 데이터 원본 가져오기
        $personsData = $totalReportData['childScaleList'][$parentCode] ?? [];
        $personsChildData = $totalReportData['childScaleList'][$parentCode]['childScaleList'][$childCode] ?? [];  // 현재 척도의 원본 하위 데이터

        // 결과지에 사용할 데이터 형태로 만들기
        $personsInfo = $this->getResultBasicInfo($personsData);
        $personsChildInfo = $this->getResultBasicInfo($personsChildData);

        $data = [
            'data' => $personsData,             // 현재 척도의 원본 데이터
            'childData' => $personsChildData,   // 현재 척도의 원본 하위 데이터
            'dataInfo' => $personsInfo,         // 결과지에 사용할 데이터 형대로 만든 현재 척도 데이터
            'childDataInfo' => $personsChildInfo,    // 결과지에 사용할 데이터 형대로 만든 현재 척도 데이터의 하위 척도 데이터
        ];

        return $data;
    }

    /**
     * 결과 json 데이터 가져오기
     * svq api를 통해 결과 데이터 받아오기.
     * @param int $applyId
     * @return array
     */
    protected function getResultJsonData($applyId)
    {
        $resultOriData = $this->_siatSvqInstance->apiResult($this->_svqApi->getExamResultData($applyId));
        if (empty($resultOriData) || empty($resultOriData['rslt_json'])) {
            $this->_alertHelper->Alert('결과 데이터가 없습니다.')->close()->end();
        }

        return json_decode((string) $resultOriData['rslt_json'], true);
    }

    /**
     *  현재 척도 데이터와 하위 척도 리스트 가져오기.
     * @param array $reportData
     * @param string $childOrder
     * @return array
     */
    protected function getResultBasicInfo($reportData, $childOrder = null)
    {
        $data = [
            'totalScaleInfo' => $this->getScaleInfo($reportData),       // 현재 척도만의 데이터 가져오기
            'childList' => $this->getScaleChildList($reportData),       // 현재 척도의 하위 데이터 리스트 가져오기
        ];

        if (!empty($childOrder)) {
            array_multisort(array_column($data['childList'], $childOrder), SORT_DESC, $data['childList']);
        }

        return $data;
    }

    /**
     * 응시자 정보 가져오기
     * 앞써 받아온 결과 데이터에서 응시자 정보만 추출한다.
     * @param array $reportData
     * @return array
     */
    protected function getReportApplyInfo($reportData)
    {
        $userData = [
            'aplId' => !empty($reportData['aplId']) ? $reportData['aplId'] : '',
            'exmtNo' => !empty($reportData['exmtNo']) ? $reportData['exmtNo'] : '',
            'applyName' => !empty($reportData['aplName']) ? $reportData['aplName'] : '',
            'examStartDt' => !empty($reportData['examStartDt']) ? str_replace(
                '-',
                '.',
                substr((string) $reportData['examStartDt'], 0, 10)
            ) : '',
        ];

        $this->applyName = $userData['applyName'];

        return $userData;
    }

    /**
     * 인적성종합 데이터 만들기
     * 앞써 가져온 종합 척도 데이터로 결과지에 보여줄 데이터 형태 만들기
     * 이런씩으로 결과 json을 받아와서 be에서 fe에 보여줄 형태를 다 만들어서 fe에 전달함.
     * @param array $reportData
     * @param string $siatSvqCategory
     * @return array
     */
    protected function getTotalResultInfo($reportData, $siatSvqCategory = 'personality_sri')
    {
        if ($siatSvqCategory == 'personality_sri') {
            return [
                'capabilitiesList' => $this->getScaleChildList($reportData['childScaleList'][355]['childScaleList'][Code::SCALE_CODE['paid_persons_sixtotal']]) ?? [], // 역량
                'suitability' => [   // 조직 적합도
                    'info' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['paid_persons_suitability']]) ?? [],
                    // 적응도
                    'adaptation' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['paid_persons_suitability']]['childScaleList'][Code::SCALE_CODE['paid_persons_stbt_adaptation']]) ?? [],
                    // 부적응도
                    'notAdaptation' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['paid_persons_suitability']]['childScaleList'][Code::SCALE_CODE['paid_persons_stbt_not_adaptation']]) ?? [],

                ],
            ];
        }

        $data = [
            'totalScaleInfo' => $this->getScaleInfo($reportData),
            // 인성
            'personsScaleInfo' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['combination_persons']] ?? []) ?? [],
            // 적성
            'aptitudeScaleInfo' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['combination_aptitude']] ?? []) ?? [],
            // 역량
            'capabilitiesList' => $this->getScaleChildList($reportData['childScaleList'][Code::SCALE_CODE['combination_capabilities']] ?? []) ?? [],

            'suitability' => [   // 조직 적합도
                'info' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['combination_suitability']] ?? []) ?? [],
                'adaptation' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['combination_suitability']]['childScaleList'][Code::SCALE_CODE['cbnt_stbt_adaptation']] ?? []) ?? [],
                // 적응도
                'notAdaptation' => $this->getScaleInfo($reportData['childScaleList'][Code::SCALE_CODE['combination_suitability']]['childScaleList'][Code::SCALE_CODE['cbnt_stbt_not_adaptation']] ?? []) ?? [],
                // 부적응도
            ],
        ];

        return $data;
    }

    /**
     * 레이더 그래프 데이터 만들기
     * d3 그래프에 작업되어 있는 json 구조로 만들기.
     * @param array $reportData
     * @return array
     */
    protected function setRadarData($reportData)
    {
        $radarData = [];
        $radarAvgData = [
            'idx' => '평균',
            'area' => [],
            'grade' => [],
        ];

        $radarMyData = [
            'idx' => '내점수',
            'area' => [],
            'grade' => [],
        ];

        $setAvgData = [];
        $setMyData = [];

        array_multisort(array_column($reportData['childScaleList'], 'scaleOrder'), SORT_ASC, $reportData['childScaleList']);
        foreach ($reportData['childScaleList'] as $row) {
            $scaleName = str_replace('유료_', '', (string) $row['scaleName']);
            $setAvgData[$scaleName] = 50;
            $setMyData[$scaleName] = number_format($row['pecentileScore']);
            array_push($radarMyData['grade'], $row['levelName']);
        }

        $radarAvgData['area'] = $setAvgData;
        $radarMyData['area'] = $setMyData;

        array_push($radarData, $radarAvgData);
        array_push($radarData, $radarMyData);

        return $radarData;
    }

    /**
     * 척도 하위 리스트 데이터 가져가기
     * 현재 데이터($reportData)의 바로 밑 하위 척도 리스트를 가져온다
     * ex) 현재 데이터 $reportData가 1번 척도 이면, 1번 척도에 걸린 바로 하위 뎁스 리스트만 가져온다.
     * 1
     *  ㄴ 2
     *      ㄴ5
     *  ㄴ 3
     *  ㄴ 4
     * 이런 경우 5번을 제외한 2,3,4 척도만 가져온다.
     * @param array $reportData
     * @return array
     */
    protected function getScaleChildList($reportData)
    {
        if (empty($reportData['childScaleList'])) {
            return [];
        }
        array_multisort(
            array_column($reportData['childScaleList'], 'scaleOrder'),
            SORT_ASC,
            $reportData['childScaleList']
        );

        $data = [];
        foreach ($reportData['childScaleList'] as $row) {
            $scaleData = $this->getScaleInfo($row);
            $scaleData['recomJobList'] = [];

            // 직무 적합도일 경우 추천직무 가져옴
            if ($reportData['scaleId'] == Code::SCALE_CODE['combination_job']) {
                $bcode = Code::COMBINATION_OCC_CODE[$row['scaleId']];
                //$occupationData = $this->getOccupationList($bcode);
                //$scaleData['recomJobList'] = $occupationData;
            }

            // 유료 인성 직무 적합도
            if ($reportData['scaleId'] == Code::SCALE_CODE['paid_persons_job']) {
                $bcode = Code::PAID_PER_OCC_CODE[$row['scaleId']];
                // $occupationData = $this->getOccupationList($bcode);
                // $scaleData['recomJobList'] = $occupationData;
            }

            array_push($data, $scaleData);
        }

        return $data;
    }

    /**
     * 직종코드 2차 목록 가져오기 - 전체 검색 코드는 덤으로 가져옴.
     * @param int $bcode
     * @return array
     */
    // protected function getOccupationList($bcode)
    // {
    //     $codeManage = new CodeManage('job');
    //     $occupationListArr = [];
    //     $searchCodeArr = [];
    //     $codeArr = !empty($bcode) ? $codeManage->getKeywordList($bcode) : [];

    //     ksort($codeArr);

    //     foreach ($codeArr as $threeDepthCode) {
    //         foreach ($threeDepthCode as $row) {
    //             if (count($searchCodeArr) < 5) {    // 통합검색 파라미터로 넘길 값들. 최대 검색수는 5개까지만 된다.
    //                 $searchCodeArr[] = $row['KEWD_CD_NO'];
    //             }

    //             if (count($occupationListArr) < 7) {
    //                 $occupationListArr[] = $row['KEWD_CD_NM'];
    //             }
    //         }
    //     }

    //     $searchCode = implode(', ', $searchCodeArr);
    //     $occupationList = implode(', ', $occupationListArr);

    //     return [
    //         'occupationList' => $occupationList,
    //         'searchCode' => $searchCode,
    //     ];
    // }

    /**
     * 척도 정보 생성하기
     * 결과지에 표기할 정보를 만든다.
     * @param array $reportData
     * @return array
     */
    protected function getScaleInfo($reportData = [])
    {
        if (empty($reportData)) {
            return [];
        }

        $scaleName = str_replace('유료_', '', (string) $reportData['scaleName']);

        $data = [
            'scaleId' => $reportData['scaleId'],    // 척도 번호
            'scaleName' => $scaleName,  // 척도명
            'scaleOrder' => $reportData['scaleOrder'],  // 척도 정렬 순서
            'gradeLevelCount' => $reportData['gradeLevelCount'],    // 척도 등급 level 총 카운트
            'level' => !empty($reportData['level']) ? $reportData['level'] : 1, // 척도 레벨
            'levelName' => !empty($reportData['levelName']) ? $reportData['levelName'] : '레벨명비었음',  // 척도 레벨(등급)명
            'pecentileScore' => !empty($reportData['pecentileScore']) ? $reportData['pecentileScore'] : 0, // 백분위
            'scaleExplain' => $this->replaceExplain($reportData['scaleExplain']),  //  해석
            'standardAvgScore' => $reportData['standardAvgScore'],    // 평균점수
            'standardScore' => $reportData['standardScore'], // 표준점수
            'originScore' => $reportData['originScore'],    // 원점수
            'interviewList' => !empty($reportData['interviewList']) ? $reportData['interviewList'] : '등록된 면접질문이 없습니다.', // 면접질문
            'description' => !empty(Code::REPORT_SCALE_DESCRIPTION[$scaleName]) ? Code::REPORT_SCALE_DESCRIPTION[$scaleName] : '해당 척도에 대한 설명이 등록되지 않았습니다.',   // 척도 description
        ];

        return $data;
    }

    /**
     * 해석 치환하기.
     * @param string $explain
     * @return string
     */
    protected function replaceExplain($explain)
    {
        $explain = str_replace('{[BR]}', '', $explain);
        $explain = str_replace('{[NAME]}', '<b>' . $this->applyName . '</b>', $explain);

        return $explain;
    }

    /**
     * 응시자 정보(사람인 + SVQ) 가져오기.
     * @param int $siatUserSeq
     * @return array
     */
    // protected function getSvqApplyInfo($siatUserSeq)
    // {
    //     $mcomIdx = $this->_getIdentity('mcom_idx');
    //     $userInfo = $this->_siatModel->getExamUserList(['siat_user_seq' => $siatUserSeq, 'mcom_idx' => $mcomIdx],
    //         ['fetch_mode' => 'row']);
    //     $svqExamInfo = $this->_svqApi->getExmtExamInfo($userInfo['auth_id'], $userInfo['auth_pw']);

    //     return !empty($svqExamInfo['data']) ? $svqExamInfo['data'] + ['exam_category' => $userInfo['exam_category']] : [];
    // }

    /**
     * 응시자 랭킹 정보 가져오기 (완료 기준으로 실시간 정보).
     * @param int $siatUserSeq
     * @param int $siatExamId
     * @param string $rsltAttrNm
     * @return array
     */
    protected function getExamRankInfo($siatUserSeq, $siatExamId, $rsltAttrNm)
    {
        // 해당 검사에 등록된 전체 카운트를 알아옴
        // 응시자 전체 카운트는 클라이언트 디비에서 등록된 응시자 수임
        $resultInfo = $this->_siatModel->getExamResultInfo(['siat_exam_seq' => $siatExamId]);
        if (count($resultInfo) < 1) {
            return [];
        }

        // 사람인 디비에 저장되어 있는 점수 기준으로 랭킹을 뽑아옴
        // 인적성 응시 완료가 되면 응시 결과 json 데이터에서 클라이언트가 별도로 관리할 데이터를 클라이언트 디비에 저장해야함.
        // 사람인은 종합 점수를 별도로 사람인 디비에 저장해둠(랭킹을 뽑아오기 위해서임)
        $rankInfo = $this->_siatModel->getExamResultRankInfo([
            'siat_user_seq' => $siatUserSeq,
            'siat_exam_seq' => $siatExamId,
            'rslt_attr_nm' => $rsltAttrNm,
        ]);

        $data = [
            'totalCount' => count($resultInfo),
            'rank' => $rankInfo['rank'],
        ];

        return $data;
    }

    /**
     * siat 인적성 검사 결과지별 인쇄시 높이값.
     * @param $exam_category
     * @return bool|mixed
     */
    protected function getExamCategoryPrintHeight($exam_category)
    {
        //검사별 결과지 높이값
        $examCategoryPrintHeightList = [
            'aptitude_huno' => 8000,
            'capability_huno' => 6000,
            'aptitude_lite_huno' => 1050,
            'capability_lite_huno' => 1050,
            'aptitude_vq' => 4200,
            'occupation_vq' => 4200,
            'capability_vq' => 2200,
            'personality_vq' => 1180,
            'personality_lite_vq' => 1180,
            'aptitude_kac' => 8200,
            'occupation_kac' => 5300,
            'capability_kac' => 5300,
            'personality_kac' => 1180,
            'tm_selection_kac' => 1180,
            'aptitude_full_fit'    => 8566,
            'aptitude_fit'         => 7190,
            'personality_full_fit' => 5826,
            'personality_fit'      => 4394,
        ];

        if (empty($examCategoryPrintHeightList[$exam_category])) {
            //default
            return 1100;
        }

        return $examCategoryPrintHeightList[$exam_category];
    }

    protected function getVNReportInfo($totalReportData)
    {
        $personsChildData = $totalReportData;
        unset($personsChildData['childScaleList'][551]);
        // 결과지에 사용할 데이터 형태로 만들기
        $personsInfo = $this->getResultBasicInfo($totalReportData);
        $personsChildInfo = $this->getResultBasicInfo($personsChildData);

        return [
            'data' => $totalReportData,             // 현재 척도의 원본 데이터
            'childData' => $personsChildData,   // 현재 척도의 원본 하위 데이터
            'dataInfo' => $personsInfo,         // 결과지에 사용할 데이터 형대로 만든 현재 척도 데이터
            'childDataInfo' => $personsChildInfo,    // 결과지에 사용할 데이터 형대로 만든 현재 척도 데이터의 하위 척도 데이터
        ];
    }

    protected function getVNTotalReportData($resultData = [])
    {
        return $resultData['result'][1]['data'][550];
    }

    protected function getVNReliabilityResultInfo($totalReportData = [])
    {
        return $this->getResultBasicInfo($totalReportData['childScaleList'][551] ?? []);
    }

    protected function setVNRadarData($reportData)
    {
        $radarData = [];
        $radarAvgData = [
            'idx' => 'Average',
            'area' => [],
            'grade' => [],
            'backgroundColor' => 'rgba(250, 100, 0, 0.27)',
            'pointBackgroundColor' => '#FA6400',
            'borderColor' => '#FA6400',
            'borderWidth' => 1,
            'pointStyle' => 'triangle',
        ];

        $radarMyData = [
            'idx' => 'MyScore',
            'area' => [],
            'grade' => [],
            'backgroundColor' => 'rgba(116,195,255,0.29)',
            'pointBackgroundColor' => '#0091FF',
            'borderColor' => '#0091FF',
            'borderWidth' => 1,
        ];

        $setAvgData = [];
        $setMyData = [];

        array_multisort(array_column($reportData['childScaleList'], 'scaleOrder'), SORT_ASC, $reportData['childScaleList']);
        foreach ($reportData['childScaleList'] as $row) {
            $scaleName = str_replace('유료_', '', (string) $row['scaleOrder']);
            $setAvgData[$scaleName] = 50;
            $setMyData[$scaleName] = number_format($row['standardScore']);
            array_push($radarMyData['grade'], $row['levelName']);
        }

        $radarAvgData['area'] = $setAvgData;
        $radarMyData['area'] = $setMyData;

        array_push($radarData, $radarAvgData);
        array_push($radarData, $radarMyData);

        return $radarData;
    }
}
