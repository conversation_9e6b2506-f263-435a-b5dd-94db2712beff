<?php

use Illuminate\Support\Facades\Route;
use Modules\PersonalityTest\Http\Controllers\CandidateController;
use Modules\PersonalityTest\Http\Controllers\DeleteResumeController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::group(['middleware' => ['auth:api']], function () {
    // Done
    Route::get('users/available-exam', [CandidateController::class, 'getAllExam']);

    Route::post('users', [CandidateController::class, 'candidateRegister']);

    // Testing
    Route::get('apply/{candidate}/exam', [CandidateController::class, 'getExamByUser'])
        ->middleware('can:personality-test-owner,candidate');

    Route::put('apply/{candidate}/exam', [CandidateController::class, 'startExam'])
        ->middleware('can:personality-test-owner,candidate');

    Route::put('apply/{candidate}/use-second', [CandidateController::class, 'updateExamTime'])
        ->middleware('can:personality-test-owner,candidate');

    Route::get('apply/{candidate}/use-second', [CandidateController::class, 'checkremainTime'])
        ->middleware('can:personality-test-owner,candidate');

    Route::post('apply/{candidate}/answer/{question_id}', [CandidateController::class, 'answerExam'])
        ->middleware('can:personality-test-owner,candidate');

    Route::get('apply/{candidate}/result-form', [CandidateController::class, 'checkLinkDocs'])
        ->middleware('can:personality-test-owner,candidate');

    Route::post('apply/{candidate}/mark', [CandidateController::class, 'markResult'])
        ->middleware('can:personality-test-owner,candidate');

    Route::get('apply/{candidate}/result', [CandidateController::class, 'getResultExam'])
        ->middleware('can:special-emails-and-owner,candidate');

    Route::get('apply/{candidate}/results', [CandidateController::class, 'getResultExams'])
        ->middleware('can:personality-test-owner,candidate');

    Route::get('apply/{candidate}', [CandidateController::class, 'getCandidate'])
        ->middleware('can:personality-test-owner,candidate');

    Route::post('apply/{candidate}/answer-json', [CandidateController::class, 'multiAnswerExam'])
        ->middleware('can:personality-test-owner,candidate');

    Route::post('/apply/{candidate}/feedback', [CandidateController::class, 'feedback'])
        ->middleware('can:personality-test-owner,candidate');

    // Api delete user data
    Route::delete('/delete-resume/{id}', [DeleteResumeController::class, 'destroy'])
        ->middleware('permission:administrator');
});
