<?php

namespace Modules\PersonalityTest\Providers;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Modules\PersonalityTest\Entities\Candidate;
use Modules\User\Entities\User;

class PersonalityTestServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        Gate::define('personality-test-owner', fn(User $user, Candidate $candidate) => $user->id === $candidate->user_id);

        Gate::define('special-emails-and-owner', function (User $user, Candidate $candidate) {
            if (in_array($user->email, Arr::wrap(config('app.special_emails')))) {
                return true;
            }

            return $user->id === $candidate->user_id;
        });

        $this->registerConfig();
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('PersonalityTest', 'Config/config.php') => config_path('ptest.php'),
        ], 'config');

        $this->mergeConfigFrom(
            module_path('PersonalityTest', 'Config/config.php'),
            'ptest'
        );
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
