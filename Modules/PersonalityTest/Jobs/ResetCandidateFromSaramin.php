<?php

namespace Modules\PersonalityTest\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PersonalityTest\Entities\Candidate;
use Modules\PersonalityTest\Helpers\Helper;

class ResetCandidateFromSaramin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $tries = 3;

    protected $candidate;

    public function __construct(Candidate $candidate)
    {
        $this->candidate = $candidate;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $response = Helper::callApi(
            'put',
            Helper::getApiUrl('apply/{candidate_id}/exam', ['candidate_id' => $this->candidate->getKey()]),
            ['type' => 'reset']
        )->json();

        if (($response['status'] ?? 0) == 200) {
            return $this->candidate->markCandidateAsDeletedFromSaramin();
        }

        throw new Exception($response);
    }
}
