<?php

namespace Modules\PersonalityTest\Jobs;

use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PersonalityTest\Entities\Candidate;
use Modules\PersonalityTest\Helpers\Helper;

class ProcessStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;

    public function __construct(protected Candidate $candidate)
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws GuzzleException
     */
    public function handle()
    {
        $candidate = $this->candidate;
        $candidate_id = $candidate->getKey();

        $apiUrl = Helper::getApiUrl('apply/{candidate_id}/exam?type=combine', ['candidate_id' => $candidate_id]);
        $res = Helper::callApi('get', $apiUrl);

        if (!isset($res['data']['exam']['apl_stus'])) {
            return;
        }

        $status = (int) $res['data']['exam']['apl_stus'];
        if ($status != Candidate::WAITING_STATUS) {
            $candidate->update([
                'status' => $status,
            ]);
        }

        if ($status === Candidate::TIMEOUT_STATUS) {
            $apiUrlMark = Helper::getApiUrl('apply/{candidate_id}/mark', ['candidate_id' => $candidate->getKey()]);
            $resMark = Helper::callApi('post', $apiUrlMark);

            if (isset($resMark['status']) && $resMark['status'] === 200) {
                $candidate->update([
                    'status' => $status,
                ]);
            }
        }

        if ($status === Candidate::COMPLETED_WITHOUT_GRADING_STATUS || $status === Candidate::COMPLETED_WITH_GRADING_STATUS) {
            $completed_at = Carbon::parse($res['data']['exam']['mod_dt'] . 'Asia/Seoul')->tz('Asia/Ho_Chi_Minh');
            $candidate->update([
                'completed_at' => $completed_at,
            ]);
        }
    }
}
