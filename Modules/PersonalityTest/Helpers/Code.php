<?php

namespace Modules\PersonalityTest\Helpers;

/**
 * 결과지에 어떤 척도를 보여줄지 몰라 svq 관리자에 등록된 모든 척도를 const 선언해서 필요한 척도만 불러와 보여준다.
 * 그 외 치환해서 보여줘야 하는 항목들을 모두 선언해둔다.
 */
class Code
{
    // 검사 타입
    public const EXAM_TYPE_PERSONALITY = 1;    // 인성검사
    public const EXAM_TYPE_APTITUDE = 2;       // 적성검사
    public const EXAM_TYPE_COMBINATION = 3;    // 인적성

    public const EXAM_TYPE = [
        self::EXAM_TYPE_PERSONALITY => '인성검사',
        self::EXAM_TYPE_APTITUDE => '적성검사',
        self::EXAM_TYPE_COMBINATION => '인적성검사',
    ];

    public const EXAM_CHYN = [
        'Y' => '유료',
        'N' => '무료',
    ];

    // 정답유형
    public const ANSWER_TYPE_OBJECT = 1;   // 객관식
    public const ANSWER_TYPE_SUBJECT = 2;  // 주관식
    public const ANSWER_TYPE_POINT_SCALE = 3;  // 점척도

    public const ANSWER_TYPE = [
        self::ANSWER_TYPE_OBJECT,
        self::ANSWER_TYPE_SUBJECT,
    ];

    // 응시자 상태
    public const APPLY_TYPE_WAITING = 0;                                                                                       // 응시 대기중
    public const APPLY_TYPE_ING = 1;                                                                                           // 응시중
    public const APPLY_TYPE_COMPLETE = 2;                                                                                      // 응시완료 - 채점전
    public const APPLY_TYPE_RESULT_COMPLETE = 12;                                                                              // 응시완료 - 채점완료
    public const APPLY_TYPE_TIME_OUT = 3;                                                                                      // 시간초과 - 상태는 응시중으로 침.
    public const APPLY_TYPE_NONE_SCORE = 13;                                                                                   // 채점 불가

    public const APPLY_STATUS = [
        self::APPLY_TYPE_WAITING => '응시대기',
        self::APPLY_TYPE_ING => '응시중',
        self::APPLY_TYPE_COMPLETE => '응시완료(채점전)',
        self::APPLY_TYPE_RESULT_COMPLETE => '응시완료',
        self::APPLY_TYPE_TIME_OUT => '응시시간만료',
        self::APPLY_TYPE_NONE_SCORE => '채점불가',
    ];

    // 응시 완료 상태
    public const APPLY_COMPLETE_STATUS = [
        self::APPLY_TYPE_COMPLETE,
        self::APPLY_TYPE_RESULT_COMPLETE,
        self::APPLY_TYPE_NONE_SCORE,
    ];

    /*
    const SARAMIN_CMM_CODE_OPERATION = 1; // 경영,사무
    const SARAMIN_CMM_CODE_SALES = 2; // 영업·고객상담
    const SARAMIN_CMM_CODE_PRODUCE = 3; // 생산·제조
    const SARAMIN_CMM_CODE_INTERNET = 4; // IT·인터넷
    const SARAMIN_CMM_CODE_PROFESSION = 5; // 전문직
    const SARAMIN_CMM_CODE_EDUCATION = 6; // 교육
    const SARAMIN_CMM_CODE_MEDIA = 7; // 미디어
    const SARAMIN_CMM_CODE_PUBLIC = 8; // 특수계층·공공
    const SARAMIN_CMM_CODE_ERECTION = 9;  // 건설
    const SARAMIN_CMM_CODE_CIRCULATION = 10;  // 유통·무역
    const SARAMIN_CMM_CODE_SERVICE = 11;  // 서비스
    const SARAMIN_CMM_CODE_DESIGN = 12; // 디자인
    const SARAMIN_CMM_CODE_MEDICAL = 13;  // 의료
    */

    public const SARAMIN_CMM_CODE_OPERATION = 14; // 마케팅·홍보·조사
    public const SARAMIN_CMM_CODE_SALES = 8; // 영업·판매·무역
    public const SARAMIN_CMM_CODE_PRODUCE = 11; // 생산
    public const SARAMIN_CMM_CODE_INTERNET = 2; // IT개발·데이터
    public const SARAMIN_CMM_CODE_PROFESSION = 9; // 연구·R&D·설계
    public const SARAMIN_CMM_CODE_EDUCATION = 19; // 교육
    public const SARAMIN_CMM_CODE_MEDIA = 13; // 미디어·문화·스포츠
    public const SARAMIN_CMM_CODE_PUBLIC = 20; // 공공·복지
    public const SARAMIN_CMM_CODE_ERECTION = 22;  // 건설
    public const SARAMIN_CMM_CODE_CIRCULATION = 7;  // 운전·운송·배송
    public const SARAMIN_CMM_CODE_SERVICE = 10;  // 서비스
    public const SARAMIN_CMM_CODE_DESIGN = 15; // 디자인
    public const SARAMIN_CMM_CODE_MEDICAL = 6;  //의료

    // 인적성 관리자에 등록된 직종코드와 사람인 직종코드 매핑 - 적성용
    public const APT_OCC_CODE = [
        82 => self::SARAMIN_CMM_CODE_OPERATION,                // 경영,사무
        83 => self::SARAMIN_CMM_CODE_SALES,     // 영업·고객상담
        84 => self::SARAMIN_CMM_CODE_INTERNET,  // IT·인터넷
        85 => self::SARAMIN_CMM_CODE_DESIGN, // 디자인
        86 => self::SARAMIN_CMM_CODE_SERVICE,    // 서비스
        87 => self::SARAMIN_CMM_CODE_PROFESSION,    // 전문직
        88 => self::SARAMIN_CMM_CODE_MEDICAL,   // 의료
        89 => self::SARAMIN_CMM_CODE_PRODUCE, // 생산·제조
        90 => self::SARAMIN_CMM_CODE_ERECTION,    // 건설
        91 => self::SARAMIN_CMM_CODE_CIRCULATION,  // 유통·무역
        92 => self::SARAMIN_CMM_CODE_MEDIA,   // 미디어
        93 => self::SARAMIN_CMM_CODE_EDUCATION,   // 교육
        94 => self::SARAMIN_CMM_CODE_PUBLIC, // 특수계층·공공
    ];

    // 인적성 관리자에 등록된 직종코드와 사람인 직종코드 매핑 - 인성용
    public const PER_OCC_CODE = [
        11 => self::SARAMIN_CMM_CODE_OPERATION,                // 경영,사무
        12 => self::SARAMIN_CMM_CODE_SALES,     // 영업·고객상담
        13 => self::SARAMIN_CMM_CODE_INTERNET,  // IT·인터넷
        14 => self::SARAMIN_CMM_CODE_DESIGN, // 디자인
        15 => self::SARAMIN_CMM_CODE_SERVICE,    // 서비스
        16 => self::SARAMIN_CMM_CODE_PROFESSION,    // 전문직
        17 => self::SARAMIN_CMM_CODE_MEDICAL,   // 의료
        18 => self::SARAMIN_CMM_CODE_PRODUCE, // 생산·제조
        19 => self::SARAMIN_CMM_CODE_ERECTION,    // 건설
        20 => self::SARAMIN_CMM_CODE_CIRCULATION,  // 유통·무역
        21 => self::SARAMIN_CMM_CODE_MEDIA,   // 미디어
        22 => self::SARAMIN_CMM_CODE_EDUCATION,   // 교육
        23 => self::SARAMIN_CMM_CODE_PUBLIC, // 특수계층·공공
    ];

    // 인적성 관리자에 등록된 직종코드와 사람인 직종코드 매핑 - 인적성용
    public const COMBINATION_OCC_CODE = [
        180 => self::SARAMIN_CMM_CODE_OPERATION,                // 경영,사무
        181 => self::SARAMIN_CMM_CODE_SALES,     // 영업·고객상담
        182 => self::SARAMIN_CMM_CODE_INTERNET,  // IT·인터넷
        183 => self::SARAMIN_CMM_CODE_DESIGN, // 디자인
        184 => self::SARAMIN_CMM_CODE_SERVICE,    // 서비스
        185 => self::SARAMIN_CMM_CODE_PROFESSION,    // 전문직
        186 => self::SARAMIN_CMM_CODE_MEDICAL,   // 의료
        187 => self::SARAMIN_CMM_CODE_PRODUCE, // 생산·제조
        188 => self::SARAMIN_CMM_CODE_ERECTION,    // 건설
        189 => self::SARAMIN_CMM_CODE_CIRCULATION,  // 유통·무역
        190 => self::SARAMIN_CMM_CODE_MEDIA,   // 미디어
        191 => self::SARAMIN_CMM_CODE_EDUCATION,   // 교육
    ];

    // 인적성 관리자에 등록된 직종코드와 사람인 직종코드 매핑 - 유료 인성용
    public const PAID_PER_OCC_CODE = [
        395 => self::SARAMIN_CMM_CODE_OPERATION,                // 경영,사무
        397 => self::SARAMIN_CMM_CODE_SALES,     // 영업·고객상담
        398 => self::SARAMIN_CMM_CODE_INTERNET,  // IT·인터넷
        399 => self::SARAMIN_CMM_CODE_DESIGN, // 디자인
        400 => self::SARAMIN_CMM_CODE_SERVICE,    // 서비스
        401 => self::SARAMIN_CMM_CODE_PROFESSION,    // 전문직
        402 => self::SARAMIN_CMM_CODE_MEDICAL,   // 의료
        403 => self::SARAMIN_CMM_CODE_PRODUCE, // 생산·제조
        404 => self::SARAMIN_CMM_CODE_ERECTION,    // 건설
        405 => self::SARAMIN_CMM_CODE_CIRCULATION,  // 유통·무역
        406 => self::SARAMIN_CMM_CODE_MEDIA,   // 미디어
        407 => self::SARAMIN_CMM_CODE_EDUCATION,   // 교육
    ];

    // 척도 코드
    public const SCALE_CODE = [
        'paid_persons_total' => 354,                // 인성 (종합데이터) //Personality (Comprehensive Data)
        'combination' => 112,                       // 인적성 //personality
        'combination_capabilities' => 116,          // 인적성- 역량 // Personality- Competence
        'combination_persons' => 113,               // 인적성- 인성 // personality - personality

        'paid_persons' => 355,                      // 유료 인성 // paid personality
        'combination_aptitude' => 114,              // 인적성- 적성
        'combination_suitability' => 115,           // 인적성 조직적합도

        'paid_persons_suitability' => 381,          // 유료 인성 조직적합도
        'paid_persons_capabilities' => 529,         // 유료 인성 역량

        'combination_totrlbt' => 192,                               // 인적성 신뢰도종합
        'combination_totrlbt_reliability' => 118,                   // 인적성 신뢰도종합 - 신뢰도
        'combination_totrlbt_reliability_consistency' => 152,       // 인적성 신뢰도종합 - 신뢰도 - 일관성
        'combination_totrlbt_reliability_positive' => 153,          // 인적성 신뢰도종합 - 신뢰도 - 과잉긍정

        'paid_persons_totrlbt' => 390,                              // 유료 인성 신뢰도종합
        'paid_persons_totrlbt_reliability' => 391,                  // 유료 인성 신뢰도종합 - 신뢰도
        'paid_persons_totrlbt_reliability_consistency' => 392,      // 유료 인성 신뢰도종합 - 신뢰도 - 일관성
        'paid_persons_totrlbt_reliability_positive' => 393,         // 유료 인성 신뢰도종합 - 신뢰도 - 과잉긍정

        'combination_job' => 179,                   // 인적성 직무적합도
        'paid_persons_job' => 394,                  // 유료 인성 직무적합도

        'combination_persons_sixtotal' => 119,      // 인적성 인성 6요인종합
        'paid_persons_sixtotal' => 356,             // 유료 인성 6요인종합
        'combination_aptitude_fourtotal' => 154,    // 인적성 적성 4영역종합

        'cbnt_cpbt_duty' => 159,                    // 인적성 - 역량 - 직무기초
        'cbnt_cpbt_work' => 160,                    // 인적성 - 역량 - 업무관리
        'cbnt_cpbt_business' => 161,                // 인적성 - 역량 - 사업관리
        'cbnt_cpbt_self' => 162,                    // 인적성 - 역량 - 자기관리
        'cbnt_cpbt_interpersonal' => 163,           // 인적성 - 역량 - 대인관리(관계관리)

        'cbnt_stbt_adaptation' => 144,              // 인적성 - 조직 적합도 - 적응도
        'cbnt_stbt_not_adaptation' => 145,          // 인적성 - 조직 적합도 - 부적응도

        'paid_persons_stbt_adaptation' => 382,          // 유료 인성 - 조직 적합도 - 적응도
        'paid_persons_stbt_not_adaptation' => 383,      // 유료 인성 - 조직 적합도 - 부적응도

        'paid_persons_soft_skill' => 490,                   // 유료 인성 - 소프트 스킬 //Paid Personality - Soft Skills
        'paid_persons_communication' => 491,                // 유료 인성 - 소프트 스킬 - 커뮤니케이션
        'paid_persons_problem_solving' => 492,              // 유료 인성 - 소프트 스킬 - 문제해결
        'paid_persons_stbt_participation' => 493,           // 유료 인성 - 소프트 스킬 - 조직참여성향
        'paid_persons_problem_solving_tendency' => 498,     // 유료 인성 - 소프트 스킬 - 문제해결성향
        'paid_persons_problem_solving_cabl' => 456,         // 유료 인성 - 소프트 스킬 - 문제해결 - 문제해결능력
        'paid_persons_organization_compliant' => 503,       // 유료 인성 - 소프트 스킬 - 조직참여성향 - 순응형
        'paid_persons_organization_medium' => 504,          // 유료 인성 - 소프트 스킬 - 조직참여성향 - 중도형
        'paid_persons_organization_innovative' => 505,      // 유료 인성 - 소프트 스킬 - 조직참여성향 - 혁신형

        'cbnt_soft_skill' => 449,                           // 인적성 - 소프트 스킬
        'cbnt_communication' => 450,                        // 인적성 - 소프트 스킬 - 커뮤니케이션
        'cbnt_problem_solving' => 451,                      // 인적성 - 소프트 스킬 - 문제해결
        'cbnt_stbt_participation' => 452,                   // 인적성 - 소프트 스킬 - 조직참여성향
        'cbnt_problem_solving_tendency' => 457,             // 인적성 - 소프트 스킬 - 문제해결성향
        'cbnt_communication_speak' => 453,                  // 인적성 - 소프트 스킬 - 커뮤니케이션 - 말하기
        'cbnt_communication_symp' => 454,                   // 인적성 - 소프트 스킬 - 커뮤니케이션 - 공감능력
        'cbnt_communication_juge' => 455,                   // 인적성 - 소프트 스킬 - 커뮤니케이션 - 감정표현
        'cbnt_problem_solving_cabl' => 456,                 // 인적성 - 소프트 스킬 - 문제해결 - 문제해결능력
        'cbnt_problem_solving_cabl_awre' => 468,            // 인적성 - 소프트 스킬 - 문제해결 - 문제해결능력 - 문제인식
        'cbnt_problem_solving_cabl_untn' => 469,            // 인적성 - 소프트 스킬 - 문제해결 - 문제해결능력 - 핵심파악
        'cbnt_problem_solving_cabl_derm' => 470,            // 인적성 - 소프트 스킬 - 문제해결 - 문제해결능력 - 결단력
        'cbnt_organization_compliant' => 462,               // 인적성 - 소프트 스킬 - 조직참여성향 - 순응형
        'cbnt_organization_medium' => 463,                  // 인적성 - 소프트 스킬 - 조직참여성향 - 중도형
        'cbnt_organization_innovative' => 464,              // 인적성 - 소프트 스킬 - 조직참여성향 - 혁신형
    ];

    // 결과 데이터 유형 코드
    public const REPORT_RESULT_CODE = [
        'persons' => 1,                 // 인성
        'persons_job' => 2,             // 인성 직무적합도
        'aptitude' => 3,                // 적성
        'aptitude_job' => 4,            // 적성 직무적합도
        'combination' => 5,             // 인적성 종합
        'combination_job' => 6,          // 인적성 종합 직무적합도
    ];

    // 씨앗 인적성 결과지 디스크립션
    public const REPORT_SCALE_DESCRIPTION = [
        '직무기초' => '자신이 의도한 바를 간결하고 명료하게 정리하고, ICT 시스템을 활용하여 문서화할 수 있으며, 청자의 요구나 특성을 고려하여 효과적으로 전달하는 능력',
        '업무관리' => '세부적인 사항을 고려하되 마감기한 내에 업무를 완수할 수 있도록 일정을 조율하고, 업무 시 발생하는 다양한 문제상황에 효율적으로 대처할 수 있는 능력',
        '관계관리' => '진솔하고 일관된 행동과 개방적인 태도로 신뢰를 구축하고, 상대방의 의견을 경청하되 논리적으로 설득할 수 있으며, 팀 목표 달성을 위해 유용한 정보를 공유하고 협력적인 분위기를 조성하는 능력',
        '자기관리' => '주어진 역할의 범위와 한계를 명확히 인식하고, 자신의 역할에 자부심을 가지고 몰두하며, 이상적인 업무 목표를 설정하여 주도적으로 업무를 수행하는 능력',
        '성과관리' => '고객의 니즈를 충족시켜 주려 힘쓰며, 조직 내외부 상황을 전략적으로 분석하고, 조직의 목표에 부합하는 성과를 낼 수 있는 방향으로 행동하는 능력',
        '정직겸손성' => '자신에게 유리한 방향으로 의사 결정하거나 사람들을 조정하지 않고, 물질적인 것에 과하게 욕심을 내지 않으며 겸손해 할 줄 아는 성향',
        '정서안정성' => '미래에 대한 긍정적 인식과 자신에 대한 믿음을 바탕으로 타인과 관계를 구축해 나가며 예상치 못한 상황에 처하더라도 심리적 안정을 유지할 수 있는 성향',
        '외향성' => '사람들과 어울리는 것을 선호하고 에너지 넘치며, 리더십 있고 과감하게 일을 추진하려는 성향',
        '성실성' => '높은 성취욕구를 바탕으로 체계적이고 신중하게 일을 추진하려는 성향',
        '원만성' => '타인을 돕는 것에서 기쁨을 느끼고, 타인의 사고방식이나 행동을 존중하고 수용하며, 업무 상황에서 적절히 협조하거나 필요한 협력을 이끌어내려는 성향',
        '개방성' => '시각적 흥미를 유발하는 것을 포함하여 새로운 것이나 방식에 관심을 갖고 깊이 탐구하며, 이를 업무 장면에 시도하고 적용하는데 거리낌이 없는 성향',
        '언어' => '단어와 문장의 의미를 정확히 이해하고, 적절한 어휘를 사용하여 대화하거나 글을 쓸 수 있는 능력<br>글에서 제시된 사실적 또는 맥락적 정보와 개념의 핵심 내용을 파악하고, 요약하여 제시할 수 있는 능력',
        '공간' => '정보를 머릿속으로 떠올리고, 부분을 통합하여 전체를 파악할 수 있는 능력<br>물체를 마음속으로 회전시키거나 조합할 수 있고, 방향을 바꾸더라도 동일한 도형을 찾아낼 수 있는 능력',
        '수리' => '수 체계/수학에 대한 이해를 바탕으로 일상적 수리 문제를 쉽게 해결할 수 있는 능력 <br>말이나 글로 된 문제 상황을 보다 간단한 수학적 기호(사칙연산 기호, 등호, 부등호 등)로 전환한 뒤 해결해내는 능력',
        '추리' => '복잡한 상황과 전제를 정확히 파악하고, 비판적 사고와 추론 과정을 통해 주장의 진위 여부 혹은 오류를 판단할 수 있는 능력 <br>주어진 사실들을 조합하여 새로운 가설을 만들고, 스스로 검증할 수 있는 능력',
    ];

    // 인적성 조직적합도 값
    public const COMBINATION_SUITABILITY_VALUE = [
        1 => '하',
        2 => '중',
        3 => '상',
    ];

    // 인성/인적성 커뮤니케이션 값
    public const COMMUNICATION_VALUE = [
        1 => '매우 부족',
        2 => '부족',
        3 => '보통',
        4 => '우수',
        5 => '매우 우수',
    ];

    // 인성/인적성 등급 값
    public const RATING_VALUE = [
        1 => 'D',
        2 => 'C',
        3 => 'B',
        4 => 'A',
        5 => 'S',
    ];

    // 인성/인적성 업무역량 값
    public const WORK_VALUE = [
        1 => 'D',
        2 => 'C',
        3 => 'B',
        4 => 'A',
        5 => 'S',
    ];

    // 인성/인적성 조직참여성향 값
    public const PARTICIPATION_VALUE = [
        0 => '성향 없음',
        1 => '순응형',
        2 => '중도형',
        3 => '혁신형',
    ];
}
