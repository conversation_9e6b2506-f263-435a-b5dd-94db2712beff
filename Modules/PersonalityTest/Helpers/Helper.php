<?php

namespace Modules\PersonalityTest\Helpers;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class Helper
{
    public const EXAM_DEFAUTL = [
        'id' => 1,
        'title' => 'bài test tính cách',
    ];

    public const PAGE_LIMIT = 10;

    public static function getApiUrl($path, $params = [])
    {
        if (count($params)) {
            foreach ($params as $key => $value) {
                $path = str_replace('{' . $key . '}', $value, (string) $path);
            }
        }

        return personality_test_url($path);
    }

    /**
     * @throws GuzzleException
     */
    public static function callApi($method, $url, $param = [])
    {
        $config = [
            'headers' => [
                'client-id' => config('ptest.client-id'),
            ],
        ];

        if (config('ptest.auth.enable')) {
            $config['auth'] = [
                config('ptest.auth.username'),
                config('ptest.auth.password'),
            ];
        }

        $client = new Client($config);

        $options = [];

        if ($method === 'post' || $method === 'put') {
            $options['form_params'] = $param;
        }

        $response = $client->request($method, $url, $options);

        return json_decode($response->getBody()->getContents(), true);
    }
}
