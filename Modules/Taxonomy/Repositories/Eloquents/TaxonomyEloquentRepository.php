<?php

namespace Modules\Taxonomy\Repositories\Eloquents;

use Modules\Taxonomy\Repositories\Contracts\TaxonomyRepositoryInterface;
use ONGR\ElasticsearchDSL\Aggregation\Bucketing\TermsAggregation;
use ONGR\ElasticsearchDSL\Query\Compound\BoolQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MatchQuery;
use ONGR\ElasticsearchDSL\Query\FullText\MultiMatchQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\ExistsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\IdsQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermQuery;
use ONGR\ElasticsearchDSL\Query\TermLevel\TermsQuery;
use ONGR\ElasticsearchDSL\Search;
use ONGR\ElasticsearchDSL\Sort\FieldSort;
use Redmix0901\Core\Repositories\Eloquent\BaseRepository;

class TaxonomyEloquentRepository extends BaseRepository implements TaxonomyRepositoryInterface
{
    //The scroll parameter tells Elasticsearch to keep the search context open for another 1m.
    public const TIME_SCROLL = '4m';

    /**
     * @inheritdoc
     */
    public function searchOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearch($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    public function searchSkillsOnElasticsearch($keyword = null, $params = [], $fields = '*')
    {
        return $this->querySearchSkills($keyword, $params, $fields)->raw();
    }

    /**
     * @inheritdoc
     */
    private function querySearch($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            //pagination
            $size = $params['page_size'];
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;
            //check if has scroll
            $_scroll_id = $params['_scroll_id'] ?? null;
            //set params to search
            $paramsSearch = [];

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường text
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['text_vi', 'text_en'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'feature')) {
                $baseQuery->add(new TermsQuery('feature', explode(',', (string) $params['feature'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'is_active')) {
                $baseQuery->add(new ExistsQuery('activated_at'));
            }

            /*
             * Tìm kiếm trên cột taxonomy
             */
            if (array_key_exists('taxonomy', $params)) {
                $baseQuery->add(new TermsQuery('taxonomy', empty($params['taxonomy']) ? [] : explode(',', (string) $params['taxonomy'])), BoolQuery::MUST);
            }

            $baseQuery->add(new TermQuery('taxonomy', 'best_skills'), BoolQuery::MUST_NOT);

            if (array_key_exists('ids', $params)) {
                $baseQuery->add(new IdsQuery(empty($params['ids']) ? [] : $this->termsQuery($params['ids'])), BoolQuery::MUST);
            }

            if ($this->hasQuery($params, 'except_ids')) {
                $baseQuery->add(new IdsQuery($this->termsQuery($params['except_ids'])), BoolQuery::MUST_NOT);
            }

            if ($this->hasQuery($params, 'keyword')) {
                $baseQuery->add(new MatchQuery('keyword', $params['keyword']), BoolQuery::MUST);
            }

            //set scroll if data > 10000
            if ($size > 10000 || $size < 0) {
                $body->setScroll(self::TIME_SCROLL);
                $body->setSize(10000);
                $paramsSearch['scroll'] = $body->getUriParams();
            } else {
                $body->setSize($size);
                $body->setFrom($from);
            }

            $body->addSort(new FieldSort('id', null, ['order' => FieldSort::ASC]));
            $body->addQuery($baseQuery);
            $body->addAggregation(new TermsAggregation('status', 'status'));

            $paramsSearch['index'] = $this->model->searchableAs();
            $paramsSearch['body'] = $body->toArray();

            if ($_scroll_id != null) {
                return $client->scroll([
                    'scroll_id' => $_scroll_id,
                    'scroll' => self::TIME_SCROLL,
                ]);
            } else {
                return $client->search($paramsSearch)->asArray();
            }
        })
        ->query(function ($builder) {
            $builder->with('term');
        });
    }

    private function querySearchSkills($keyword, $params, $fields)
    {
        return $this->model->search('*', function ($client, $body) use ($keyword, $params) {
            //pagination
            $size = $params['page_size'];
            $from = isset($params['page']) ? $size * ($params['page'] - 1) : 0;
            $search_after = $params['search_after'] ?? null;

            $baseQuery = new BoolQuery();

            /*
             * Tìm kiếm keyword các trường text
             */
            if (!empty($keyword)) {
                $baseQuery->add(new MultiMatchQuery(['text_vi', 'text_en'], $keyword, ['operator' => 'or']), BoolQuery::MUST);
            }

            /*
             * Tìm kiếm trên cột taxonomy
             */
            if (array_key_exists('taxonomy', $params)) {
                $baseQuery->add(new TermsQuery('taxonomy', empty($params['taxonomy']) ? [] : explode(',', (string) $params['taxonomy'])), BoolQuery::MUST);
            }

            $body->setSize($size);
            if ($search_after != null) {
                $body->setSearchAfter($search_after);
                $body->setFrom(-1);
            } else {
                $body->setFrom($from);
            }

            $paramsSearch['index'] = $this->model->searchableAs();
            $paramsSearch['body'] = $body->toArray();

            $body->addSort(new FieldSort('id', null, ['order' => FieldSort::ASC]));
            $body->addQuery($baseQuery);

            $body->addAggregation(new TermsAggregation('status', 'status'));

            return $client->search(['index' => $this->model->searchableAs(), 'body' => $body->toArray()])->asArray();
        })
        ->query(function ($builder) {
            $builder->with('term');
        });
    }

    private function termsQuery($string, $intval = false)
    {
        return collect(explode(',', (string) $string))->reject(function ($value, $intval) {
            if ($intval) {
                return empty($value) && intval($value);
            }

            return empty($value);
        })->values()->all();
    }

    /**
     * Check if has query.
     */
    private function hasQuery($query, $key)
    {
        return (bool) (isset($query[$key]) && !is_null($query[$key]));
    }
}
