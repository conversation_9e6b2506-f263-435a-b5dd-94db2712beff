<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="vendor/autoload.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
            <directory suffix="Test.php">./Modules/Payment/Tests/Unit</directory>
            <directory suffix="Test.php">./Modules/Order/Tests/Unit</directory>
        </testsuite>

        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
            <directory suffix="Test.php">./Modules/Payment/Tests/Feature</directory>
            <directory suffix="Test.php">./Modules/Order/Tests/Feature</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist>
            <directory suffix=".php">./app</directory>
            <directory suffix=".php">./Modules/Payment</directory>
            <directory suffix=".php">./Modules/Order</directory>
        </whitelist>
    </filter>
    <php>
        <server name="APP_ENV" value="testing"/>
    </php>
</phpunit>
