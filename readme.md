# About Applancer Manager System (AMS)

- <PERSON><PERSON> thống quản lý dữ liệu hệ thống Applancer thông qua nhiều loại hình kết nối.

## Setup

1. Env

   Config environment symlink

```bash
    ln -sF env/.env.local .env
```

2. Local

```bash
    composer install
    composer all-scripts
```

3. Production

```bash
    composer install
    composer setup
    rm -rf app/Admin
    php artisan admin:install
    composer import-extension
    composer modules
```

4. Supervisor conf

```bash
    ln -sF $(pwd)/supervisor/conf.d/ams-queue.conf /etc/supervisor/conf.d/ams-queue.conf
```

5. Create Permistion

```bash
    php artisan db:seed --class=PermistionTableSeeder
```

## Audit

```bash
    php artisan vendor:publish --provider "OwenIt\Auditing\AuditingServiceProvider" --tag="config"
    php artisan vendor:publish --provider "OwenIt\Auditing\AuditingServiceProvider" --tag="migrations"
```

6. Feature flag usage
    ```php
        // Use class before using
        use App\Facades\Unleash;

        // Check feature flag with name 'online-payment' is enabled or disabled.
        if (Unleash::isEnabled('online-payment')) {
            // Logic if feature is enabled
        } else {
            // Logic if feature is disable also known as old feature/flow/logic.
        }
    ```

7. Horizon:

    If using topdop docker (It's recommended), let pull `main` branch an then:
    - nginx: restart to take effect
    - topdop: Rebuild this container. Then start worker service by command:
        ```
        docker compose up -d worker
        ```
    If not, need setting these things (Not recommended):
    1. Check document to install supervisor: [Deploying Horizon](https://laravel.com/docs/6.x/horizon#deploying-horizon)
    2. Update your api nginx with adding 2 configs
        ```sh
            location ~^/horizon {
                # assume @php-api is your fastgi alias
                try_files $uri $uri/ @php-api;
            }

            location ~^/vendor/horizon/ {
                # assume `/var/dev/api/public;` is your api location
                root /var/dev/api/public;
            }
        ```
    3. Restart nginx

## Session config

Example config using redis connection

```
## Session
SESSION_DRIVER=redis
SESSION_CONNECTION=accounts
SESSION_LIFETIME=1200
SESSION_DOMAIN=.10.topdev.asia
SESSION_COOKIE=TDSID
SESSION_SECURE_COOKIE=true
SESSION_SAMESITE_COOKIE=none

## Redis
REDIS_URL=redis-1:9000,redis-2:9000,redis-3:9000
REDIS_HOST=redis
REDIS_PREFIX="{test-api}:"
REDIS_AMS_PREFIX="{test-ams}:"
REDIS_ACCOUNTS_PREFIX="{test-accounts}:"
REDIS_MAIL_PREFIX="{test-mail}:"
```

Full configuration can be found in `.env` on `stg-102` at `/var/www/upgrade-10x/.env`

## Redis cluster

See this issue for more information: 
- https://github.com/laravel/horizon/issues/274

Horizon not officially support redis cluster, but we can use it by using `daison/laravel-horizon-cluster` package.

## GitLab Auto DevOps

### CNB: Cloud Native Buildpacks

```yaml
build:
  variables:
    #AUTO_DEVOPS_BUILD_IMAGE_FORWARDED_CI_VARIABLES: ALLOW_EOL_SHIMMED_BUILDER,PASSPORT_PRIVATE_KEY,LOG_CHANNEL
    AUTO_DEVOPS_BUILD_IMAGE_CNB_BUILDER: heroku/builder:22
    BUILDPACK_URL: heroku/php,heroku/procfile
```
- `ALLOW_EOL_SHIMMED_BUILDER`, `AUTO_DEVOPS_BUILD_IMAGE_FORWARDED_CI_VARIABLES` are set in [Admin Area](https://git.topdev.asia/admin/application_settings/ci_cd)

Procfile (/cnb/process)
- web fpm_custom.conf, nginx_include.conf
- schedule
- queue
- horizon

* [Heroku CNB Builder Images](https://github.com/heroku/cnb-builder-images)
* [A temporary solution to the problem that occurs when using nginx as a web server.](https://github.com/heroku/buildpacks-php/issues/73)  

### Review App (deployment)

with worker-deployment (`workers: {schedule:, horizon:}`)

mount /app/.env from K8S_SECRET_DOTENV_FILE, NFS (`/srv/data/assets` for production, `/srv/data/resources` for review)
```yaml
HELM_UPGRADE_EXTRA_ARGS: --values .gitlab/${CI_ENVIRONMENT_SLUG}-secret.volume.yaml
```


### Edit .env secret and Rollout Restart

Decode the Secret
```shell
kubectl get secret review-upgrade-10-n1sk51-secret -n api-78-review-upgrade-10-n1sk51 -o jsonpath='{.data.DOTENV}' | base64 --decode > .env.local-k3s
```

```shell
# kubectl config use-context topdev/cluster-management:kube-dev && kubectl get ns
#kubect edit secret ${APPLICATION_SECRET_NAME:-${CI_ENVIRONMENT_SLUG}-secret} -n api-78-review-upgrade-10-n1sk51
kubectl edit secret review-upgrade-10-n1sk51-secret -n api-78-review-upgrade-10-n1sk51
# Edit DOTENV content (base64 < .env.local-k3s)

# Restart all deployments in account-23-review-upgrade-10-n1sk51 namespace
kubectl rollout restart deployment -n api-78-review-upgrade-10-n1sk51

# monitoring log
kubectl logs -n account-23-review-upgrade-10-n1sk51 -l release=review-upgrade-10-n1sk51 --since=10m
```

### GitLab Auto Build and Deploy to local k3s

```shell
docker compose --project-directory ../../devops/compose-topdev-environments config build-topdev-api local-topdev-api
```

build and deploy to local k3s
```shell
docker compose --project-directory ../../devops/compose-topdev-environments run build-topdev-api && \
docker compose --project-directory ../../devops/compose-topdev-environments run local-topdev-api  
```

### [Browser Performance Testing](https://docs.gitlab.com/ee/ci/testing/browser_performance_testing.html)

`.gitlab-urls.txt`

