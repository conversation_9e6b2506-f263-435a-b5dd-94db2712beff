/node_modules
/public/hot
/public/storage
/public/resources
/public/facebook-job-feed
/public/docs
/public/vendor/laravel-admin/minify-manifest.json
/storage/*.key
/storage/medialibrary/
/storage/gg_credentials/token.json
/storage/facebook-job-feed
/storage/google-job-feed
/vendor
/tools/vendor
.env
.env.*
!.env.example
http-client.private.env.json
adsapi_php.ini
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
/app/Admin
*/.DS_Store
env/.env.sonnx
env/.env.local
storage/*.json
.vscode
.idea/
storage/gg_credentials/token.json
storage/gg_credentials/client_id.json
storage/my_gg_credentials
storage/my_gg_credentials/token.json
storage/my_gg_credentials/credentials.json
/supervisor/conf.d/api-queue-nhan.conf
storage/firebase_credentials
.idea
.env.testing
.php-cs-fixer.cache
.phpstan
storage/test
.phpunit.cache/test-results

# gitlab auto devops
/gl-auto-build-variables.env
/environment_url.txt
/chart
/.gitlab/*.volume.yaml
/gitlab-exporter/
/sitespeed-results/

