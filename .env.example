APP_NAME=TopDev
APP_ENV=local
APP_DEBUG=true
APP_KEY=base64:l39HZcjMp2LAO3YXsMdYsm3/QINcuMdsBbVHA6TAVnY=

## Path Config
APP_URL=http://${BASE_DOMAIN}
ASSETS_URL=http://${ASSETS_DOMAIN}
FRONTEND_URL=http://${FRONTEND_DOMAIN}
TAOCV_URL=http://${FRONTEND_DOMAIN}/tao-cv-online

## Debugbar Config
DEBUGBAR_ENABLED=false

## Api Config
API_PREFIX=/td/v2
API_URL=http://${API_DOMAIN}

ALLOWED_ORIGINS=*

## Log config
LOG_CHANNEL=daily
BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=database

## Main Database Config
DB_CONNECTION=mysql
DB_HOST=mysql
DB_DATABASE=ams
DB_USERNAME=root
DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
DB_PORT=3306

DB_HOST_CVBUILDER=mysql
DB_PORT_CVBUILDER=3306
DB_DATABASE_CVBUILDER=cvbuilder
DB_USERNAME_CVBUILDER=root
DB_PASSWORD_CVBUILDER=${MYSQL_ROOT_PASSWORD}

DB_HOST_REPORT=mysql
DB_PORT_REPORT=3306
DB_DATABASE_REPORT=td_reports
DB_USERNAME_REPORT=root
DB_PASSWORD_REPORT=${MYSQL_ROOT_PASSWORD}

## Elasticsearch config
ELASTICSEARCH_HOST=http://es:9200

DELAY_VIEW_JOB=3600

## Filesystem config
FILESYSTEM_DRIVER=prod
AMS_FILESYSTEM_PATH=/var/dev/resources

## Email service config
MAIL_DRIVER=smtp
MAIL_ENCRYPTION=tls
MAIL_HOST=smtp.sendgrid.net
MAIL_PASSWORD=@pplancer2014
MAIL_PORT=587
MAIL_USERNAME=1

## Model cache
MODEL_CACHE_ENABLED=false

## TopDev SSO Config
OAUTH2_URL_RESOURCE_OWNER=http://${ACCOUNTS_DOMAIN}/api/v1/user

## Rabbitmq Config
RABBITMQ_HOST=rabbitmq
RABBITMQ_LOGIN=guest
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_PORT=5672
RABBITMQ_VHOST=/

## Redis config
REDIS_HOST=redis
REDIS_CLIENT=predis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_PREFIX=null
REDIS_DB=4
REDIS_CACHE_DB=3

## Scout elasticsearch
SCOUT_DRIVER=Matchish\ScoutElasticSearch\Engines\ElasticSearchEngine
SCOUT_QUEUE=true
SCOUT_QUEUE_NAME=import_elasticsearch
SCOUT_QUEUE_USING=database

## Sentry
SENTRY_LARAVEL_DSN=

## Session config
SESSION_DRIVER=file
SESSION_DOMAIN=.${BASE_DOMAIN}
SESSION_COOKIE=topdev_session
SESSION_PATH=/var/dev/accounts/storage/framework/sessions

## Telegram
CHAT_ID=1
TELEGRAM_BOT_ID=1
TELEGRAM_BOT_TOKEN=1ieieie
TOKEN_HACKERRANK=8b9f6be1ba2c9a51320cb67227779a9ff175e1cc5ade40a13767e673d8bac035

## App Version Config
APP_IOS_VERSION_CURRENT=2.0.1
APP_IOS_FORCE_UPDATE=true
APP_ANDROID_VERSION_CURRENT=1.0.1
APP_ANDROID_FORCE_UPDATE=true

## Google service account
GOOGLE_SERVICE_ENABLED=true
GOOGLE_SERVICE_ACCOUNT_JSON_LOCATION=gg_credentials/topdev-report-cmd.json

## Increase number for search
INCREASE_TOTAL=1.6

## Personality Test
PERSONALITY_TEST_CLIENT_ID=2
PERSONALITY_TEST_API_URL=http://api_saramin.topdev.asia/api
PERSONALITY_TEST_AUTH_ENABLE=true
PERSONALITY_TEST_AUTH_USERNAME=apisvq
PERSONALITY_TEST_AUTH_PASSWORD=
UNLEASH_APP_URL=https://git.topdev.asia/api/v4/feature_flags/unleash/155
UNLEASH_INSTANCE_ID=w5zjmb-MySGvaED8Dhm3

## Payment gateway
# Megapay
PAYMENT_MEGAPAY_API_URL=https://sandbox.megapay.vn
PAYMENT_MEGAPAY_MER_ID=EPAY000001
PAYMENT_MEGAPAY_ENCODE_KEY=rf8whwaejNhJiQG2bsFubSzccfRc/iRYyGUn6SPmT6y/L7A2XABbu9y4GvCoSTOTpvJykFi6b1G0crU8et2O0Q==
PAYMENT_MEGAPAY_JS_URL=https://sandbox.megapay.vn/pg_was/js/payment/layer/paymentClient.js

# Momo
PAYMENT_MOMO_API_URL=https://test-payment.momo.vn
PAYMENT_MOMO_PARTNER_CODE=MOMO3WJ120240126_TEST
PAYMENT_MOMO_ACCESS_KEY=fYV1S6X4yraLUm6B
PAYMENT_MOMO_SECRET_KEY=Tn3mjLzORIwmqzZFktDvmh4xiuaIqRJk
PAYMENT_MOMO_PUBLIC_KEY=

# VAT percent for calculating
VAT_PERCENT=8

# CRM API
CRM_API_BASE_URL=https://crmdev.topdev.asia
CRM_API_AUTH_CODE=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZ3d0RRWUpLb1pJaHZjTkFRRUJCUUFEU3dBd1NBSkJBTkdWNGdzUHk3bzlEcVI3TzQ3K0ZpM0ltNEROa2h3dAp2Nk9OSkJoLytjYkJSZlRnSGRGcExSS1NQTDZ4eHozSjFRK0Y2WlBRR0YrZ2dlald0YStXVHZjQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo=

## Laravel Sactum
SANCTUM_STATEFUL_DOMAINS=${BASE_DOMAIN}

## Laravel Horizon
HORIZON_ADMIN_EMAILS=
HORIZON_QUEUE_CONNECTION=database

## collect timeout
SESSION_TIMEOUT_NUMBER=30
