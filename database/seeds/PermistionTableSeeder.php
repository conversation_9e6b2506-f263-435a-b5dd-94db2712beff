<?php

use Illuminate\Database\Seeder;

class PermistionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $permissions = collect(\Route::getRoutes())->map(function ($route) {

            $uri_arr = explode('admin/', $route->uri());
            if (strpos($route->uri(), '_debugbar') === false && strpos($route->uri(), 'admin/auth') === false) {
                $permissions = [
                    'group_key'   => md5(current(explode('@', ltrim($route->getActionName())))),
                    'group_name'  => ucwords(strstr(str_replace('admin.', '', $route->getName()), '.', true)),
                    'name'        => ucwords(str_replace(['admin.', '.'], ['', ' '], $route->getName()), ' '),
                    'slug'        => str_replace('admin.', '', $route->getName()),
                    'http_method' => implode(',', $route->methods()),
                    'http_path'   => implode('/', $uri_arr),
                ];
                return $permissions;
            }
        });

        foreach ($permissions as $k => $v) {
            if (!is_null($v) && !empty($v['group_name'])) {
                if (!isset($http_path[$v['group_key']])) {
                    $http_path[$v['group_key']][] = $v['http_path'];
                }else if (!in_array($v['http_path'], $http_path[$v['group_key']])) {
                    $http_path[$v['group_key']][] = $v['http_path'];
                }

                $i_group_permission[$v['group_key']]['name']         = $v['group_name'] . ' management';
                $i_group_permission[$v['group_key']]['slug']         = strtolower($v['group_name']) . '.management';
                $i_group_permission[$v['group_key']]['http_path']    = implode("\r\n", $http_path[$v['group_key']]);
                $i_permission[$k]['name']         = $v['name'];
                $i_permission[$k]['slug']         = $v['slug'];
                $i_permission[$k]['http_method']  = $v['http_method'];
                $i_permission[$k]['http_path']    = $v['http_path'];

            }else{
                unset($permissions[$k]);
            }
        }

        $permissionModel = config('admin.database.permissions_model');

        $permissionModel::where('id', '>', 11)->delete();

        $permissionModel::insert($i_group_permission);
        $permissionModel::insert($i_permission);

    }
}
