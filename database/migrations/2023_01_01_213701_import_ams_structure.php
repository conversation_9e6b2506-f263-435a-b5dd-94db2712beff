<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class ImportAmsStructure extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Please note:
        // If you're running for unit testing, only run this command bellow with a fresh DB
        // php artisan migrate --path=database/migrations/2023_12_30_213701_import_ams_structure.php

        // run sql query inside file ams-structure.sql
        $sql = file_get_contents(database_path('migrations/ams-structure.sql'));
        DB::unprepared($sql);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
