parameters:
	ignoreErrors:
		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/ClickApply24h.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommend.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommendApplyAll3030.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommendApplySingle3030.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommendCvCompleted3010.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommendCvCompleted33.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/JobRecommendCvCompleted71.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/JobRecommendCvIncompleted71.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/JobRecommendJobLikeThis33.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/JobRecommendJobLikeThis71.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/JobRecommendLogin33.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/JobRecommendLogin71.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, bool given\\.$#"
			count: 1
			path: Modules/Activity/Campaigns/PushCompleteCV.php

		-
			message: "#^Function token_jobs not found\\.$#"
			count: 2
			path: Modules/Activity/Campaigns/SavedJob24h.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:setErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/AutoMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/AutoMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:setErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 3
			path: Modules/Activity/EmailMarketing/JobMauticEmailMarketing.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$area_id\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$collection\\.$#"
			count: 4
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$companies\\.$#"
			count: 2
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$jobs\\.$#"
			count: 2
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$skills\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$viewed_at\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:\\$visitor\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:area\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:area\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:companies\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:companies\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Company\\\\Entities\\\\Company\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:jobs\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:skills\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:skills\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphedByMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\<Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:trackingUser\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:trackingUser\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:user\\(\\) has invalid return type Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Entities\\\\Activity\\:\\:user\\(\\) should return Modules\\\\Activity\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/Activity/Entities/Activity.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\ActivityCreated\\:\\:activity\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$view$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\ActivityCreated\\:\\:\\$activity \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\) does not accept Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\ActivityCreated\\:\\:\\$activity has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/ActivityCreated.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:user\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^PHPDoc tag @param for parameter \\$user with type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User is not subtype of native type Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Parameter \\$user of method Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:\\$user \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\:\\:\\$user has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/EventRepuestProcessed.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array\\<mixed, mixed\\> given\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignCompleted\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignCompleted.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:getErrors\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignIncompleted\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignIncompleted.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:toArray\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:campaign\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:campaign\\(\\) should return Modules\\\\Activity\\\\Contracts\\\\CampaignContract but returns Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^PHPDoc tag @param for parameter \\$campaign with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array\\<mixed, mixed\\> given\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Parameter \\$campaign of method Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:__construct\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:\\$campaign \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\) does not accept Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\:\\:\\$campaign has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Contracts\\\\CampaignContract as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/PublishCampaignStarting.php

		-
			message: "#^Method Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:activity\\(\\) has invalid return type Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$view$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:\\$activity \\(Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\) does not accept Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Property Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\:\\:\\$activity has unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity as its type\\.$#"
			count: 1
			path: Modules/Activity/Events/SaveViewProcessed.php

		-
			message: "#^Anonymous function has an unused use \\$collectionName\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/API/ProgressController.php

		-
			message: "#^Cannot call method first\\(\\) on string\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/API/ProgressController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Method Modules\\\\Activity\\\\Http\\\\Controllers\\\\ActivityController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Activity/Http/Controllers/ActivityController.php

		-
			message: "#^Call to method header\\(\\) on an unknown class Modules\\\\Activity\\\\Jobs\\\\Illuminate\\\\Http\\\\Request\\.$#"
			count: 3
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Method Modules\\\\Activity\\\\Jobs\\\\ActivityLoggerProcess\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Method Modules\\\\Activity\\\\Jobs\\\\ActivityLoggerProcess\\:\\:request\\(\\) has invalid return type Modules\\\\Activity\\\\Jobs\\\\Illuminate\\\\Http\\\\Request\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Method Modules\\\\Activity\\\\Jobs\\\\ActivityLoggerProcess\\:\\:request\\(\\) should return Modules\\\\Activity\\\\Jobs\\\\Illuminate\\\\Http\\\\Request but returns Illuminate\\\\Http\\\\Request\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Parameter \\#1 \\$request of class Modules\\\\Activity\\\\Services\\\\Visitor constructor expects Illuminate\\\\Http\\\\Request, Modules\\\\Activity\\\\Jobs\\\\Illuminate\\\\Http\\\\Request given\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Parameter \\#3 \\$user of class Modules\\\\Activity\\\\Services\\\\Visitor constructor expects string\\|null, int given\\.$#"
			count: 1
			path: Modules/Activity/Jobs/ActivityLoggerProcess.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:publishCampaignUsing\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Jobs/MakePublishCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Entities\\\\Viewable\\:\\:\\$view_id\\.$#"
			count: 2
			path: Modules/Activity/Jobs/RemoveViewsOnDelete.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:publishCampaign\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Jobs/WebhookUpdateDataMautic.php

		-
			message: "#^Call to method listen\\(\\) on an unknown class Modules\\\\Activity\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 6
			path: Modules/Activity/Listeners/PublishActivityEvent.php

		-
			message: "#^Method Modules\\\\Activity\\\\Listeners\\\\PublishActivityEvent\\:\\:subscribe\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Listeners/PublishActivityEvent.php

		-
			message: "#^Parameter \\$events of method Modules\\\\Activity\\\\Listeners\\\\PublishActivityEvent\\:\\:subscribe\\(\\) has invalid type Modules\\\\Activity\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/Activity/Listeners/PublishActivityEvent.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\:\\:getProgress\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Listeners/SyncProgressCvBuilderToElasticsearch.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\PublishCampaignStarting\\.$#"
			count: 1
			path: Modules/Activity/Listeners/SyncProgressCvBuilderToElasticsearch.php

		-
			message: "#^Call to method only\\(\\) on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\EventRepuestProcessed\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array given\\.$#"
			count: 1
			path: Modules/Activity/Listeners/UnsearchableCvBuilderCompleted.php

		-
			message: "#^Access to property \\$user on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^Access to property \\$viewable on an unknown class Modules\\\\Activity\\\\Events\\\\Modules\\\\Activity\\\\Entities\\\\Activity\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\SaveViewProcessed\\.$#"
			count: 1
			path: Modules/Activity/Listeners/VistorOnViewable.php

		-
			message: "#^Anonymous function has an unused use \\$keyword\\.$#"
			count: 1
			path: Modules/Activity/Repositories/Eloquents/ActivityEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Activity/Repositories/Eloquents/ActivityEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/Activity/Repositories/Eloquents/ActivityEloquentRepository.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:\\$exists\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:fill\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:prepareToAttachViewables\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Call to an undefined static method Modules\\\\Activity\\\\Contracts\\\\ActivityContract\\:\\:created\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ActivityManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\CampaignManager\\:\\:createSavedJob24hDriver\\(\\) has invalid return type Modules\\\\Activity\\\\Campaigns\\\\SavedJob\\.$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\CampaignManager\\:\\:createSavedJob24hDriver\\(\\) should return Modules\\\\Activity\\\\Campaigns\\\\SavedJob but returns Modules\\\\Activity\\\\Campaigns\\\\SavedJob24h\\.$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Activity/Services/CampaignManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$detector$#"
			count: 1
			path: Modules/Activity/Services/CrawlerDetector.php

		-
			message: "#^Call to an undefined method Modules\\\\Activity\\\\Services\\\\Mautic\\\\AccessToken\\:\\:isExpirationTimestamp\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Instantiated class Modules\\\\Activity\\\\Services\\\\Mautic\\\\InvalidArgumentException not found\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Instantiated class Modules\\\\Activity\\\\Services\\\\Mautic\\\\RuntimeException not found\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Throwing object of an unknown class Modules\\\\Activity\\\\Services\\\\Mautic\\\\InvalidArgumentException\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Throwing object of an unknown class Modules\\\\Activity\\\\Services\\\\Mautic\\\\RuntimeException\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/AccessToken.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/Mautic.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:accessTokenUpdated\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:getAccessTokenData\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Call to an undefined method Mautic\\\\Auth\\\\AuthInterface\\:\\:validateAccessToken\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getClient\\(\\) has invalid return type Mautic\\\\MauticConsumer\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getClient\\(\\) should return Mautic\\\\MauticConsumer but return statement is missing\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getMauticUrl\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Mautic\\\\url\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:getMauticUrl\\(\\) should return Modules\\\\Activity\\\\Services\\\\Mautic\\\\url but returns string\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:make\\(\\) has invalid return type Mautic\\\\Config\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Mautic\\\\MauticFactory\\:\\:make\\(\\) should return Mautic\\\\Config but returns Mautic\\\\MauticConsumer\\.$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^PHPDoc tag @throws with type ClientException is not subtype of Throwable$#"
			count: 1
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Parameter \\#2 \\$uri of method GuzzleHttp\\\\Client\\:\\:request\\(\\) expects Psr\\\\Http\\\\Message\\\\UriInterface\\|string, Modules\\\\Activity\\\\Services\\\\Mautic\\\\url given\\.$#"
			count: 2
			path: Modules/Activity/Services/Mautic/MauticFactory.php

		-
			message: "#^Access to an undefined property Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:\\$suggest_jobs_with_utm\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Call to method clickApply\\(\\) on an unknown class Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Call to method followedJobs\\(\\) on an unknown class Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Call to method map\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\Collection\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getFilesCv\\(\\) should return array but returns string\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:getUserInstance\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Model\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:logSimilarJob\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\ProgressManager\\:\\:toJobArrayFromEloquentModel\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^PHPDoc tag @return with type mixed is not subtype of native type Modules\\\\Activity\\\\Contracts\\\\CampaignContract\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/ProgressManager.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\UserCvBuilder\\:\\:newCollection\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Collection but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$visitor$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Parameter \\#1 \\$year of static method Carbon\\\\Carbon\\:\\:create\\(\\) expects DateTimeInterface\\|int\\|null, string given\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/UserCvBuilder.php

		-
			message: "#^Call to method map\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\Collection\\.$#"
			count: 1
			path: Modules/Activity/Services/UserFeed.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/UserFeed.php

		-
			message: "#^Method Modules\\\\Activity\\\\Services\\\\Visitor\\:\\:crawlerDetector\\(\\) has invalid return type Modules\\\\Activity\\\\Services\\\\Modules\\\\Activity\\\\Services\\\\CrawlerDetector\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^PHPDoc tag @return with type Modules\\\\Activity\\\\Services\\\\Modules\\\\Activity\\\\Services\\\\CrawlerDetector is not subtype of native type Modules\\\\Activity\\\\Services\\\\CrawlerDetector\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^PHPDoc tag @return with type string\\|null is not subtype of native type string\\.$#"
			count: 1
			path: Modules/Activity/Services/Visitor.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/Activity/Services/VisitorFeed.php

		-
			message: "#^Cannot call method pluck\\(\\) on array\\<Modules\\\\Admin\\\\Auth\\\\Database\\\\Role\\>\\.$#"
			count: 3
			path: Modules/Admin/Auth/Database/Administrator.php

		-
			message: "#^PHPDoc tag @return with type mixed is not subtype of native type Illuminate\\\\Support\\\\Collection\\.$#"
			count: 1
			path: Modules/Admin/Auth/Database/Administrator.php

		-
			message: "#^PHPDoc tag @return with type mixed is not subtype of native type bool\\.$#"
			count: 3
			path: Modules/Admin/Auth/Database/Administrator.php

		-
			message: "#^Class Amscore\\\\Admin\\\\Auth\\\\Database\\\\Administrator not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Class Amscore\\\\Admin\\\\Grid\\\\Displayers\\\\DropdownActions not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Class App\\\\Http\\\\Controllers\\\\Admin\\\\AuthController not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Class App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Nav\\\\Modules\\\\Admin\\\\Auth\\\\Database\\\\Administrator not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Class App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Nav\\\\Modules\\\\Admin\\\\Auth\\\\Database\\\\Permission not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Class App\\\\Http\\\\Controllers\\\\Admin\\\\Extensions\\\\Nav\\\\Modules\\\\Admin\\\\Auth\\\\Database\\\\Role not found\\.$#"
			count: 1
			path: Modules/Admin/Config/config.php

		-
			message: "#^Call to method delete\\(\\) on an unknown class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager\\.$#"
			count: 1
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Call to method download\\(\\) on an unknown class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager\\.$#"
			count: 1
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Call to method move\\(\\) on an unknown class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager\\.$#"
			count: 1
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Call to method newFolder\\(\\) on an unknown class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager\\.$#"
			count: 1
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Call to method upload\\(\\) on an unknown class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager\\.$#"
			count: 1
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Instantiated class Modules\\\\Admin\\\\Http\\\\Controllers\\\\MediaManager not found\\.$#"
			count: 5
			path: Modules/Admin/Http/Controllers/MediaController.php

		-
			message: "#^Access to an undefined property Modules\\\\Advert\\\\Entities\\\\Advert\\:\\:\\$type_banner\\.$#"
			count: 3
			path: Modules/Advert/Entities/Advert.php

		-
			message: "#^Method Modules\\\\Advert\\\\Entities\\\\Advert\\:\\:groups\\(\\) has invalid return type Modules\\\\Advert\\\\Entities\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/Advert/Entities/Advert.php

		-
			message: "#^Method Modules\\\\Advert\\\\Entities\\\\Advert\\:\\:groups\\(\\) should return Modules\\\\Advert\\\\Entities\\\\BelongsToMany but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\Advert\\\\Entities\\\\Group\\>\\.$#"
			count: 1
			path: Modules/Advert/Entities/Advert.php

		-
			message: "#^Method Modules\\\\Advert\\\\Entities\\\\Group\\:\\:ads\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\Advert\\\\Entities\\\\Advert\\>\\.$#"
			count: 1
			path: Modules/Advert/Entities/Group.php

		-
			message: "#^Method Modules\\\\Advert\\\\Http\\\\Controllers\\\\AdvertController\\:\\:all\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Advert/Http/Controllers/AdvertController.php

		-
			message: "#^Access to an undefined property Modules\\\\Advert\\\\Transformers\\\\AdsResource\\:\\:\\$destination_url\\.$#"
			count: 1
			path: Modules/Advert/Transformers/AdsResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Advert\\\\Transformers\\\\AdsResource\\:\\:\\$tracking_url\\.$#"
			count: 1
			path: Modules/Advert/Transformers/AdsResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Advert\\\\Transformers\\\\AdsResource\\:\\:getCustomImageResize\\(\\)\\.$#"
			count: 1
			path: Modules/Advert/Transformers/AdsResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Advert\\\\Transformers\\\\AdsResource\\:\\:getDestinationParamsAttribute\\(\\)\\.$#"
			count: 1
			path: Modules/Advert/Transformers/AdsResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Advert/Transformers/AdsResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$deleted_at\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$model_id\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$model_type\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Entities\\\\Announcement\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Announcement/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\Announcement\\\\Http\\\\Controllers\\\\AnnouncementController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Announcement/Http/Controllers/AnnouncementController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Announcement/Repositories/Eloquents/AnnouncementRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Announcement/Repositories/Eloquents/AnnouncementRepository.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementCollection.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\)\\:\\:\\$body\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\)\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Announcement\\\\Transformers\\\\StatusJobAnnouncementResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Announcement/Transformers/StatusJobAnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:\\$refreshed_at\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:\\$status\\.$#"
			count: 2
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:\\$title\\.$#"
			count: 2
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Result of method Modules\\\\Blog\\\\Entities\\\\FacebookDatabase\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Blog/Entities/FacebookDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\ItReportDatabase\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\ItReportDatabase\\:\\:\\$status\\.$#"
			count: 2
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\ItReportDatabase\\:\\:\\$title\\.$#"
			count: 2
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^Expression in empty\\(\\) is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Blog/Entities/ItReportDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$alias\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$banner\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$eventObject\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$logo\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$status\\.$#"
			count: 2
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\Meetup\\\\Event\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Blog/Entities/Meetup/Event.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\TechBlog\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Blog/Entities/TechBlog.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:\\$status\\.$#"
			count: 2
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:\\$title\\.$#"
			count: 2
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Result of method Modules\\\\Blog\\\\Entities\\\\YoutubeDatabase\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Blog/Entities/YoutubeDatabase.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/API/ApiMeetupController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Method Modules\\\\Blog\\\\Http\\\\Controllers\\\\BlogController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Blog/Http/Controllers/BlogController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/FacebookEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/FacebookEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/ItReportEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/ItReportEloquentRepository.php

		-
			message: "#^Method Modules\\\\Blog\\\\Repositories\\\\Eloquents\\\\ItReportEloquentRepository\\:\\:hasQuery\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/ItReportEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/MeetupEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/MeetupEloquentRepository.php

		-
			message: "#^Method Modules\\\\Blog\\\\Repositories\\\\Eloquents\\\\MeetupEloquentRepository\\:\\:hasQuery\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/MeetupEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/TechBlogEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/TechBlogEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/YoutubeEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/YoutubeEloquentRepository.php

		-
			message: "#^Method Modules\\\\Blog\\\\Repositories\\\\Eloquents\\\\YoutubeEloquentRepository\\:\\:hasQuery\\(\\) is unused\\.$#"
			count: 1
			path: Modules/Blog/Repositories/Eloquents/YoutubeEloquentRepository.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\EventMeetupCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupCollection.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\EventMeetupCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$alias\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$banner\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$logo\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$object\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\EventMeetupResource\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/EventMeetupResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/EventObjectMeetupResource.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\ItReportCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportCollection.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\ItReportCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportCollection.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\)\\:\\:\\$expires_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\)\\:\\:\\$refreshed_at\\.$#"
			count: 2
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$files_itreport\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$image_thumbnail\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\ItReportResource\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Array has 2 duplicate keys with value 'refreshed_at' \\('refreshed_at', 'refreshed_at'\\)\\.$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/ItReportResource.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostFacebookCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookCollection.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostFacebookCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$images_facebook\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$refreshed_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostFacebookResource\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/PostFacebookResource.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostTechblogCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogCollection.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostTechblogCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$ID\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$permalink\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$post_content\\.$#"
			count: 2
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$post_date\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$post_modified\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$post_name\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$post_title\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostTechblogResource\\:\\:\\$thumbnail\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/PostTechblogResource.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostYoutubeCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeCollection.php

		-
			message: "#^Method Modules\\\\Blog\\\\Transformers\\\\PostYoutubeCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$images_youtube\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$link_youtube\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Blog\\\\Transformers\\\\PostYoutubeResource\\:\\:\\$viewCount\\.$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Blog/Transformers/PostYoutubeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Blog\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Blog\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to constant FILE_DELETE_FLAG on an unknown class Amscore\\\\Admin\\\\Form\\\\Field\\.$#"
			count: 2
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Blog.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$features\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$introduces\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$products\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addresses\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Call to method active\\(\\) on an unknown class Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Called 'pluck' on Laravel collection, but could have been retrieved as a query\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addTaxonomies\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Traits\\\\Collection\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addTaxonomies\\(\\) should return Modules\\\\Taxonomy\\\\Traits\\\\Collection but returns \\$this\\(Modules\\\\Company\\\\Entities\\\\Company\\)\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:feature\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:setPackagesAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\Company but return statement is missing\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:setServicesAttribute\\(\\) should return Modules\\\\Company\\\\Entities\\\\Company but return statement is missing\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:subscription\\(\\) has invalid return type Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Method Modules\\\\Company\\\\Entities\\\\Company\\:\\:subscription\\(\\) should return Modules\\\\Subscription\\\\Traits\\\\Modules\\\\Subscription\\\\Entities\\\\Package\\|null but returns Modules\\\\Subscription\\\\Entities\\\\Package\\|null\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^PHPDoc tag @return with type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany is incompatible with native type string\\.$#"
			count: 2
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Company\\\\Entities\\\\Company\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Company/Entities/Company.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Introduce\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Introduce\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Access to constant FILE_DELETE_FLAG on an unknown class Amscore\\\\Admin\\\\Form\\\\Field\\.$#"
			count: 2
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Introduce.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Product\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Product\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Undefined variable\\: \\$file_name$#"
			count: 2
			path: Modules/Company/Entities/Product.php

		-
			message: "#^Method Modules\\\\Company\\\\Jobs\\\\CreateCompanyProcess\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/Company/Jobs/CreateCompanyProcess.php

		-
			message: "#^Method Modules\\\\Company\\\\Jobs\\\\UpdateCompanyProcess\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 2
			path: Modules/Company/Jobs/UpdateCompanyProcess.php

		-
			message: "#^Property Modules\\\\Company\\\\Jobs\\\\UpdateCompanyProcess\\:\\:\\$author is never read, only written\\.$#"
			count: 1
			path: Modules/Company/Jobs/UpdateCompanyProcess.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Repositories/Eloquents/CompanyEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Repositories/Eloquents/CompanyEloquentRepository.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$categories_id\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$categories_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$description_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$extra_skills_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$follow_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_cover\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_galleries\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$image_logo\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$industries_id\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$industries_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$members\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$nationalities_id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_followeres\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_job_openings\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_jobs\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$skills_id\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$skills_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$social_network\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:getTaxonomies\\(\\)\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource\\:\\:relationLoaded\\(\\)\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/CompanyFromEloquentResource.php

		-
			message: "#^Method Modules\\\\Company\\\\Transformers\\\\CompanyGroupCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupCollection.php

		-
			message: "#^Method Modules\\\\Company\\\\Transformers\\\\CompanyGroupCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyGroupResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyGroupResource\\:\\:\\$id\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyGroupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyGroupResource\\:\\:\\$image_cover\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyGroupResource\\:\\:\\$members\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/CompanyGroupResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Company\\\\Transformers\\\\CompanyResource\\)\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Company\\\\Transformers\\\\CompanyResource\\)\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$categories_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$categories_ids\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$categories_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$company_size\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$description_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$detail_url\\.$#"
			count: 2
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$draft\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$employees\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$extra_skills\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$faqs\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$features\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$follow_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$highlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$id\\.$#"
			count: 8
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$image_galleries\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$image_logo\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$industries_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$industries_ids\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$industries_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$introduces\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$is_highlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$is_spotlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$job_opening_url\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$members\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_description_en\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_description_vi\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_keywords_en\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_keywords_vi\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_title_en\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$meta_title_vi\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$nationalities\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$nationalities_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$nationalities_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$news\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_employees\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_followeres\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_job_openings\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_jobs\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$products\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$recruitment_process\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$schema_job_posting\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$schema_local_business\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$skills_arr\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$skills_ids\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$skills_str\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$social_network\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$spotlight\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$tagline\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$text\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\CompanyResource\\:\\:\\$website\\.$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 3
			path: Modules/Company/Transformers/CompanyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\EmployeeResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/Company/Transformers/EmployeeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/EmployeeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\IntroduceResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/IntroduceResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Transformers\\\\ProductResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Company/Transformers/ProductResource.php

		-
			message: "#^Method Modules\\\\File\\\\DetectFile\\\\DetectProcess\\:\\:detect\\(\\) with return type void returns \\$this\\(Modules\\\\File\\\\DetectFile\\\\DetectProcess\\) but should not return anything\\.$#"
			count: 1
			path: Modules/File/DetectFile/DetectProcess.php

		-
			message: "#^Method Modules\\\\File\\\\Engines\\\\ConvertProcess\\:\\:convert\\(\\) with return type void returns \\$this\\(Modules\\\\File\\\\Engines\\\\ConvertProcess\\) but should not return anything\\.$#"
			count: 1
			path: Modules/File/Engines/ConvertProcess.php

		-
			message: "#^Call to method associate\\(\\) on an unknown class Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:candidates\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:cloneFile\\(\\) should return string but returns false\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:uploadBy\\(\\) has invalid return type Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Method Modules\\\\File\\\\Entities\\\\Media\\:\\:uploadBy\\(\\) should return Modules\\\\File\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<Modules\\\\User\\\\Entities\\\\User\\>\\.$#"
			count: 1
			path: Modules/File/Entities/Media.php

		-
			message: "#^Array has 2 duplicate keys with value 'image_galleries\\.required' \\('image_galleries\\.required', 'image_galleries\\.required'\\)\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/CompanyController.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphTo\\:\\:disableCache\\(\\)\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/MediaController.php

		-
			message: "#^Cannot call method shouldBeSearchable\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/File/Http/Controllers/API/MediaController.php

		-
			message: "#^Call to an undefined method Spatie\\\\MediaLibrary\\\\Models\\\\Media\\:\\:queueMakeConvertMediaWithMarkdown\\(\\)\\.$#"
			count: 1
			path: Modules/File/Listeners/HandleMediaUploaded.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Spatie\\\\MediaLibrary\\\\Events\\\\MediaHasBeenAdded\\.$#"
			count: 1
			path: Modules/File/Listeners/HandleMediaUploaded.php

		-
			message: "#^Call to method listen\\(\\) on an unknown class Modules\\\\File\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/File/Listeners/PublishFileEvent.php

		-
			message: "#^Method Modules\\\\File\\\\Listeners\\\\PublishFileEvent\\:\\:subscribe\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/File/Listeners/PublishFileEvent.php

		-
			message: "#^Parameter \\$events of method Modules\\\\File\\\\Listeners\\\\PublishFileEvent\\:\\:subscribe\\(\\) has invalid type Modules\\\\File\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/File/Listeners/PublishFileEvent.php

		-
			message: "#^Call to an undefined static method Illuminate\\\\Support\\\\ServiceProvider\\:\\:createPermission\\(\\)\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Menu\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method find\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Menu\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Call to static method first\\(\\) on an unknown class Modules\\\\File\\\\Providers\\\\Role\\.$#"
			count: 1
			path: Modules/File/Providers/FileServiceProvider.php

		-
			message: "#^Anonymous function has an unused use \\$keyword\\.$#"
			count: 1
			path: Modules/File/Repositories/Eloquents/MediaEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/File/Repositories/Eloquents/MediaEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/File/Repositories/Eloquents/MediaEloquentRepository.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/File/Services/ConvertNewMarkdownManager.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/File/Services/DetectNewFile.php

		-
			message: "#^Call to an undefined method Spatie\\\\MediaLibrary\\\\Models\\\\Media\\:\\:getPathFolder\\(\\)\\.$#"
			count: 1
			path: Modules/File/Services/PathGenerator.php

		-
			message: "#^Access to an undefined property Modules\\\\File\\\\Transformers\\\\AssetsResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/File/Transformers/AssetsResource.php

		-
			message: "#^Access to an undefined property Modules\\\\File\\\\Transformers\\\\AssetsResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/File/Transformers/AssetsResource.php

		-
			message: "#^Call to an undefined method Modules\\\\File\\\\Transformers\\\\AssetsResource\\:\\:getFullUrl\\(\\)\\.$#"
			count: 1
			path: Modules/File/Transformers/AssetsResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/File/Transformers/AssetsResource.php

		-
			message: "#^Method Modules\\\\File\\\\Transformers\\\\AssetsResourceCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/File/Transformers/AssetsResourceCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/File/Transformers/AssetsResourceCollection.php

		-
			message: "#^Call to method groupBy\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\Collection\\.$#"
			count: 2
			path: Modules/File/Transformers/AttachedResumeCollection.php

		-
			message: "#^Call to method keyBy\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\Collection\\.$#"
			count: 2
			path: Modules/File/Transformers/AttachedResumeCollection.php

		-
			message: "#^Call to method pluck\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\Collection\\.$#"
			count: 3
			path: Modules/File/Transformers/AttachedResumeCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeCollection.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\File\\\\Transformers\\\\AttachedResumeResource\\)\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\File\\\\Transformers\\\\AttachedResumeResource\\)\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\File\\\\Transformers\\\\AttachedResumeResource\\)\\:\\:\\$resume_name\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\File\\\\Transformers\\\\AttachedResumeResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\File\\\\Transformers\\\\AttachedResumeResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Dead catch \\- Exception is never thrown in the try block\\.$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 3
			path: Modules/File/Transformers/AttachedResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_info\\.$#"
			count: 1
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$device_token\\.$#"
			count: 1
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Access to an undefined property Modules\\\\Firebase\\\\Entities\\\\DeviceToken\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/Firebase/Entities/DeviceToken.php

		-
			message: "#^Call to method listen\\(\\) on an unknown class Modules\\\\Firebase\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/Firebase/Listeners/PublishFireBaseEvent.php

		-
			message: "#^Method Modules\\\\Firebase\\\\Listeners\\\\PublishFireBaseEvent\\:\\:subscribe\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Firebase/Listeners/PublishFireBaseEvent.php

		-
			message: "#^Parameter \\$events of method Modules\\\\Firebase\\\\Listeners\\\\PublishFireBaseEvent\\:\\:subscribe\\(\\) has invalid type Modules\\\\Firebase\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/Firebase/Listeners/PublishFireBaseEvent.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$pivot\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getStatusDraft\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getStatusReview\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Call to an undefined static method Modules\\\\Job\\\\Entities\\\\Job\\:\\:withTrashed\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addTaxonomies\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Traits\\\\Collection\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addTaxonomies\\(\\) should return Modules\\\\Taxonomy\\\\Traits\\\\Collection but returns \\$this\\(Modules\\\\Job\\\\Entities\\\\Job\\)\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyClose\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyClose\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyDraft\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyDraft\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyOpen\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyOpen\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyReview\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeOnlyReview\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeStatus\\(\\) has invalid return type App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Method Modules\\\\Job\\\\Entities\\\\Job\\:\\:scopeStatus\\(\\) should return App\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^PHPDoc tag @return with type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany is incompatible with native type string\\.$#"
			count: 2
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Job\\\\Entities\\\\Job\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Entities/Job.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/CandidateController.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:\\$userRepository\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Binary operation \"\\-\" between 23 and string results in an error\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Method Modules\\\\Job\\\\Http\\\\Controllers\\\\JobController\\:\\:job\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobResource but returns Redmix0901\\\\Core\\\\Http\\\\Responses\\\\BaseHttpResponse\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Variable \\$arrText might not be defined\\.$#"
			count: 1
			path: Modules/Job/Http/Controllers/JobController.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/ItJobRequest.php

		-
			message: "#^Access to property \\$scopeMeta on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Access to property \\$scopeTerm on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Class Modules\\\\Job\\\\Http\\\\Requests\\\\Job not found\\.$#"
			count: 2
			path: Modules/Job/Http/Requests/StoreJobRequest.php

		-
			message: "#^Access to property \\$scopeMeta on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Access to property \\$scopeTerm on an unknown class Modules\\\\Job\\\\Http\\\\Requests\\\\Job\\.$#"
			count: 1
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Class Modules\\\\Job\\\\Http\\\\Requests\\\\Job not found\\.$#"
			count: 2
			path: Modules/Job/Http/Requests/UpdateJobRequest.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$case is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$hasLogin is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$jobIds is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$jobsApplied is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$payload is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\ApplyProcess\\:\\:\\$user_resume is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/ApplyProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\CreateJobProcess\\:\\:\\$author is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/CreateJobProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\CreateJobProcess\\:\\:\\$params is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/CreateJobProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\UpdateJobProcess\\:\\:\\$author is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/UpdateJobProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\UpdateJobProcess\\:\\:\\$method is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/UpdateJobProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\UpdateJobProcess\\:\\:\\$model is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/UpdateJobProcess.php

		-
			message: "#^Property Modules\\\\Job\\\\Jobs\\\\UpdateJobProcess\\:\\:\\$params is never read, only written\\.$#"
			count: 1
			path: Modules/Job/Jobs/UpdateJobProcess.php

		-
			message: "#^Property Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\BaseCacheDecorator\\:\\:\\$cache_time \\(Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\cache_time\\) does not accept int\\.$#"
			count: 1
			path: Modules/Job/Repositories/Caches/JobCacheDecorator.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getStatusDescription\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$query of class ONGR\\\\ElasticsearchDSL\\\\Query\\\\FullText\\\\MatchQuery constructor expects string, int given\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/CandidateEloquentRepository.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getStatusColumn\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getStatusReview\\(\\)\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:withoutSyncingToSearch\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Call to function array_key_exists\\(\\) with 'ids' and string will always evaluate to false\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'blacklist_companies' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'categories_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'company' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'contract_types_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'currency' on \\*NEVER\\* in isset\\(\\) always exists and is always null\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'except_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'except_skills' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'experiences_id' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'experiences_ids_or' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'fake' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'features' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'full_address' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'industries_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'job_levels_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'job_levels_ids_or' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'job_type' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'job_types_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'level' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'not_sort' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'order_region_ids' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'packages' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'page' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'page_size' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'popular' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'q' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'region_ids' does not exist on string\\.$#"
			count: 3
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'salary_range' on string in isset\\(\\) does not exist\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'sk' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'skills_arr' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'skills_id' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'skills_id_or' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'skills_str' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Offset 'status' does not exist on string\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$search of function array_key_exists expects array, string given\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$terms of class ONGR\\\\ElasticsearchDSL\\\\Query\\\\TermLevel\\\\TermsQuery constructor expects array, Illuminate\\\\Support\\\\Collection\\<int, mixed\\> given\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 2
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Right side of && is always false\\.$#"
			count: 1
			path: Modules/Job/Repositories/Eloquents/JobEloquentRepository.php

		-
			message: "#^Static method Illuminate\\\\Support\\\\Facades\\\\Route\\:\\:middleware\\(\\) invoked with 3 parameters, 1 required\\.$#"
			count: 1
			path: Modules/Job/Routes/api.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Repositories\\\\Contracts\\\\JobRepositoryInterface\\:\\:withoutCache\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Services/JobBelongToUser.php

		-
			message: "#^Method Modules\\\\Job\\\\Transformers\\\\CandidateCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$apply_end_of_week\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$delivered_at\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$files_cv\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$files_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$is_remove_cv\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$is_solving_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$job\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$resume\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$status\\.$#"
			count: 2
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$status_logs\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\CandidateResource\\:\\:\\$tags_hackerrank\\.$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Parameter \\#1 \\$time of static method Carbon\\\\Carbon\\:\\:parse\\(\\) expects DateTimeInterface\\|string\\|null, int given\\.$#"
			count: 3
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 6
			path: Modules/Job/Transformers/CandidateResource.php

		-
			message: "#^Call to method map\\(\\) on an unknown class Redmix0901\\\\ElasticResource\\\\collect\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$apply_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$company\\.$#"
			count: 4
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$experiences_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$experiences_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$extra_skills_id\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_levels_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_levels_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_types_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_types_name\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$job_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_followers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_ready_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$recruiment_process\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$requirements\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$responsibilities\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$salary\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$schema_job_posting\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$skills_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$skills_name\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource\\:\\:relationLoaded\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Job/Transformers/JobFromEloquentResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\Job\\\\Transformers\\\\JobResource\\)\\:\\:\\$id\\.$#"
			count: 3
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$apply_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$benefits\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$blog_posts\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$blog_tags\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$content\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$contract_types_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$contract_types_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$contract_types_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$detail_url\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$employer_note\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$experiences_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$experiences_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$experiences_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$extra_skills\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$features\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$id\\.$#"
			count: 8
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$image_thumbnail\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$is_basic\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$is_basic_plus\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$is_distinction\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$is_free\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_levels_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_levels_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_levels_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_types_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_types_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_types_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$job_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$meta_description\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$meta_keywords\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$meta_title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_followers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_ready_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_unqualified_candidates\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$num_viewers\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$other_supports\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$owned_id\\.$#"
			count: 6
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$packages\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$recruiment_process\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$requirements\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$responsibilities\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$salary\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$sidebar_image_banner_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$sidebar_image_link\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$skills_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$skills_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$skills_str\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$status_display\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$text\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\:\\:first\\(\\)\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Transformers/JobResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$experiences_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$features\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$job_levels_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$owned_id\\.$#"
			count: 2
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$salary\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$skills_arr\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$skills_ids\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Transformers\\\\JobV2Resource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 3
			path: Modules/Job/Transformers/JobV2Resource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\)\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\)\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$content\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$title\\.$#"
			count: 2
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Result of method Modules\\\\MessageCampaign\\\\Entities\\\\MessageCampaign\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/MessageCampaign/Entities/MessageCampaign.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Repositories/Eloquents/MessageCampaignRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/MessageCampaign/Repositories/Eloquents/MessageCampaignRepository.php

		-
			message: "#^Method Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$action_button\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$body\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$campaign_expect\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$title\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\MessageCampaign\\\\Transformers\\\\MessageCampaignResource\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/MessageCampaign/Transformers/MessageCampaignResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$data\\.$#"
			count: 1
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$notifiable_id\\.$#"
			count: 1
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$notifiable_type\\.$#"
			count: 1
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$read_at\\.$#"
			count: 2
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$seen_at\\.$#"
			count: 2
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Entities\\\\DatabaseNotification\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/Notification/Entities/DatabaseNotification.php

		-
			message: "#^Call to method listen\\(\\) on an unknown class Modules\\\\Notification\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 2
			path: Modules/Notification/Listeners/PublishNotificationEvent.php

		-
			message: "#^Method Modules\\\\Notification\\\\Listeners\\\\PublishNotificationEvent\\:\\:subscribe\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Notification/Listeners/PublishNotificationEvent.php

		-
			message: "#^Parameter \\$events of method Modules\\\\Notification\\\\Listeners\\\\PublishNotificationEvent\\:\\:subscribe\\(\\) has invalid type Modules\\\\Notification\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/Notification/Listeners/PublishNotificationEvent.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Notification/Repositories/Eloquents/NotificationEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/Notification/Repositories/Eloquents/NotificationEloquentRepository.php

		-
			message: "#^Method Modules\\\\Notification\\\\Transformers\\\\NotificationCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/Notification/Transformers/NotificationCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Notification/Transformers/NotificationCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Transformers\\\\NotificationResource\\:\\:\\$data\\.$#"
			count: 1
			path: Modules/Notification/Transformers/NotificationResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Notification\\\\Transformers\\\\NotificationResource\\:\\:\\$uuid\\.$#"
			count: 1
			path: Modules/Notification/Transformers/NotificationResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Notification/Transformers/NotificationResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Call to method filter\\(\\) on an unknown class Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\toArray\\.$#"
			count: 2
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Call to method getTraceAsString\\(\\) on an unknown class Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\Exception\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Call to static method error\\(\\) on an unknown class Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\Log\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Caught class Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\Exception not found\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:getPage\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\API\\\\ApiPageController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Variable \\$vmdSurveySheet might not be defined\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/API/ApiPageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Http\\\\Controllers\\\\PageController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Page/Http/Controllers/PageController.php

		-
			message: "#^Method Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\VmdMiniGameSheet\\:\\:getData\\(\\) has invalid return type Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\toArray\\.$#"
			count: 1
			path: Modules/Page/Services/GoogleSheet/VmdMiniGameSheet.php

		-
			message: "#^Method Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\VmdMiniGameSheet\\:\\:getData\\(\\) should return Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\toArray but returns Illuminate\\\\Support\\\\Collection\\.$#"
			count: 1
			path: Modules/Page/Services/GoogleSheet/VmdMiniGameSheet.php

		-
			message: "#^Variable \\$values on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: Modules/Page/Services/GoogleSheet/VmdMiniGameSheet.php

		-
			message: "#^Access to an undefined property Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\VmdSurveySheet\\:\\:\\$sheetNane\\.$#"
			count: 2
			path: Modules/Page/Services/GoogleSheet/VmdSurveySheet.php

		-
			message: "#^Property Modules\\\\Page\\\\Services\\\\GoogleSheet\\\\VmdSurveySheet\\:\\:\\$sheetId is unused\\.$#"
			count: 1
			path: Modules/Page/Services/GoogleSheet/VmdSurveySheet.php

		-
			message: "#^Method Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\EmailRemindPersonalityTest\\:\\:handle\\(\\) should return int but return statement is missing\\.$#"
			count: 1
			path: Modules/PersonalityTest/Console/Commands/EmailRemindPersonalityTest.php

		-
			message: "#^Access to property \\$spreadsheets_values on an unknown class Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\Google_Service_Sheets\\.$#"
			count: 2
			path: Modules/PersonalityTest/Console/Commands/ExportCandidateSheets.php

		-
			message: "#^Property Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\ExportCandidateSheets\\:\\:\\$googleService \\(Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\Google_Service_Sheets\\) does not accept Google\\\\Service\\\\Sheets\\.$#"
			count: 1
			path: Modules/PersonalityTest/Console/Commands/ExportCandidateSheets.php

		-
			message: "#^Property Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\ExportCandidateSheets\\:\\:\\$googleService has unknown class Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\Google_Service_Sheets as its type\\.$#"
			count: 1
			path: Modules/PersonalityTest/Console/Commands/ExportCandidateSheets.php

		-
			message: "#^Access to undefined constant Illuminate\\\\Console\\\\Command\\:\\:SUCCESS\\.$#"
			count: 1
			path: Modules/PersonalityTest/Console/Commands/MarkAsTimeoutPersonalityTestIncomplete.php

		-
			message: "#^Method Modules\\\\PersonalityTest\\\\Console\\\\Commands\\\\SyncDataFromSaramin\\:\\:handle\\(\\) should return int but return statement is missing\\.$#"
			count: 1
			path: Modules/PersonalityTest/Console/Commands/SyncDataFromSaramin.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Entities\\\\Candidate\\:\\:\\$user\\.$#"
			count: 1
			path: Modules/PersonalityTest/Entities/Candidate.php

		-
			message: "#^Class Modules\\\\PersonalityTest\\\\Entities\\\\User not found\\.$#"
			count: 1
			path: Modules/PersonalityTest/Entities/Candidate.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Http\\\\Controllers\\\\SaraminController\\:\\:\\$_alertHelper\\.$#"
			count: 1
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Http\\\\Controllers\\\\SaraminController\\:\\:\\$_siatModel\\.$#"
			count: 2
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Http\\\\Controllers\\\\SaraminController\\:\\:\\$_siatSvqInstance\\.$#"
			count: 1
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Http\\\\Controllers\\\\SaraminController\\:\\:\\$_svqApi\\.$#"
			count: 1
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Access to an undefined property Modules\\\\PersonalityTest\\\\Http\\\\Controllers\\\\SaraminController\\:\\:\\$applyName\\.$#"
			count: 2
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 10
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Parameter \\#1 \\$array1 of function array_multisort is passed by reference, so it expects variables only\\.$#"
			count: 4
			path: Modules/PersonalityTest/Http/Controllers/SaraminController.php

		-
			message: "#^Method Modules\\\\PersonalityTest\\\\Jobs\\\\ResetCandidateFromSaramin\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/PersonalityTest/Jobs/ResetCandidateFromSaramin.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$content\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$expires_at\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$published_at\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Post\\\\Entities\\\\Post\\:\\:\\$scopeMeta\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:getType\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Modules\\\\Post\\\\Entities\\\\Post\\:\\:author\\(\\)\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addTaxonomies\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Traits\\\\Collection\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addTaxonomies\\(\\) should return Modules\\\\Taxonomy\\\\Traits\\\\Collection but returns \\$this\\(Modules\\\\Post\\\\Entities\\\\Post\\)\\.$#"
			count: 2
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Entities\\\\Post\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\Post\\\\Entities\\\\Post\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Result of method Modules\\\\Post\\\\Entities\\\\Post\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/Post/Entities/Post.php

		-
			message: "#^Method Modules\\\\Post\\\\Http\\\\Controllers\\\\PostController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Post/Http/Controllers/PostController.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Package\\:\\:\\$term_id\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$expires_at\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$taxonomy_id\\.$#"
			count: 1
			path: Modules/Subscription/Console/HandlingIfSubscriptionExpired.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$slug\\.$#"
			count: 2
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Feature.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Term\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Package.php

		-
			message: "#^Access to an undefined property Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:\\$taxonomy_id\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Call to an undefined method Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:customRelation\\(\\)\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Entities\\\\Subscription\\:\\:term\\(\\) has invalid return type App\\\\Traits\\\\customRelation\\.$#"
			count: 1
			path: Modules/Subscription/Entities/Subscription.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:create\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:destroy\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:edit\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:show\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\View\\\\View\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Method Modules\\\\Subscription\\\\Http\\\\Controllers\\\\SubscriptionController\\:\\:update\\(\\) should return Illuminate\\\\Http\\\\Response but return statement is missing\\.$#"
			count: 1
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Parameter \\#1 \\$view of function view expects view\\-string\\|null, string given\\.$#"
			count: 3
			path: Modules/Subscription/Http/Controllers/SubscriptionController.php

		-
			message: "#^Call to private method withoutGlobalScopes\\(\\) of parent class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<TRelatedModel of Illuminate\\\\Database\\\\Eloquent\\\\Model,TChildModel of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxable.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$expires_at\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_description\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_keywords\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$meta_title\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$scopeMeta\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$sort_order\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$banner_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$description\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$thumbnail_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany\\:\\:view\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:isForceDeleting\\(\\)\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Result of method Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Taxonomy.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\hasOne\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:taxonomies\\(\\) should return Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\>\\.$#"
			count: 1
			path: Modules/Taxonomy/Entities/Terms.php

		-
			message: "#^Binary operation \"/\" between string and 10000 results in an error\\.$#"
			count: 2
			path: Modules/Taxonomy/Http/Controllers/TaxonomyController.php

		-
			message: "#^Property Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\BaseCacheDecorator\\:\\:\\$cache_time \\(Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\cache_time\\) does not accept int\\.$#"
			count: 1
			path: Modules/Taxonomy/Repositories/Caches/TaxonomyCacheDecorator.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/Taxonomy/Repositories/Eloquents/TaxonomyEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 3
			path: Modules/Taxonomy/Repositories/Eloquents/TaxonomyEloquentRepository.php

		-
			message: "#^Right side of && is always false\\.$#"
			count: 1
			path: Modules/Taxonomy/Repositories/Eloquents/TaxonomyEloquentRepository.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:getExpiresAt\\(\\) has invalid return type Carbon\\\\Carbon\\\\Carbon\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:getTaxonomy\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:setExpiresAt\\(\\) should return Modules\\\\Taxonomy\\\\Services\\\\Plan but empty return statement found\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Parameter \\#1 \\$time of static method Carbon\\\\Carbon\\:\\:parse\\(\\) expects DateTimeInterface\\|string\\|null, \\(int\\|false\\) given\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$expires_at \\(Carbon\\\\Carbon\\\\Carbon\\|null\\) does not accept Carbon\\\\Carbon\\.$#"
			count: 4
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$expires_at has unknown class Carbon\\\\Carbon\\\\Carbon as its type\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$taxonomy \\(Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\) does not accept Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\.$#"
			count: 2
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Property Modules\\\\Taxonomy\\\\Services\\\\Plan\\:\\:\\$taxonomy has unknown class Modules\\\\Taxonomy\\\\Services\\\\Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy as its type\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/Plan.php

		-
			message: "#^Call to static method info\\(\\) on an unknown class Modules\\\\Taxonomy\\\\Services\\\\Log\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: Modules/Taxonomy/Services/TaxonomyService.php

		-
			message: "#^Method Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$banner_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyEloquentResource\\:\\:\\$thumbnail_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyEloquentResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$banner_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$detail_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$feature\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$id\\.$#"
			count: 4
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$image\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$sort_order\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$taxonomy\\.$#"
			count: 4
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$text\\.$#"
			count: 4
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$text_en\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$text_vi\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Transformers\\\\TaxonomyResource\\:\\:\\$thumbnail_url\\.$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 2
			path: Modules/Taxonomy/Transformers/TaxonomyResource.php

		-
			message: "#^Access to an undefined property \\$this\\(Modules\\\\User\\\\Entities\\\\Announcement\\)\\:\\:\\$frequency\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\:\\:orwhereRaw\\(\\) invoked with 3 parameters, 1\\-2 required\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\Announcement\\:\\:scopeAlreadyReceiveAnnouncement\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\Announcement\\:\\:scopeAlreadyReceiveAnnouncement\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/User/Entities/Announcement.php

		-
			message: "#^Access to undefined constant Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:APPROVED_AT\\.$#"
			count: 1
			path: Modules/User/Entities/Scopes/ApprovedScope.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$created_at\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$model_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Company\\\\Entities\\\\Company\\:\\:\\$image_logo_url\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Job\\\\Entities\\\\Job\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Taxonomy\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$files_cv\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$files_cvbuilder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$years_of_exp\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:addresses\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:meta\\(\\)\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to an undefined method Serializable\\:\\:toArray\\(\\)\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to function is_null\\(\\) with array\\<int, mixed\\>\\|mixed will always evaluate to false\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Call to method get\\(\\) on an unknown class Collection\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Cannot call method isForceDeleting\\(\\) on class\\-string\\|object\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Cannot cast Ramsey\\\\Uuid\\\\UuidInterface to string\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\belongsTo\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphToMany referenced with incorrect case\\: Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\morphToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Illuminate\\\\Database\\\\Eloquent\\\\Collection\\<Plank\\\\Metable\\\\Meta\\> does not accept Plank\\\\Metable\\\\Meta\\|null\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Instantiated class Modules\\\\User\\\\Notifications\\\\NotifyJobAnnouncement not found\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addMetaCollection\\(\\) with return type void returns void but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addOneTerm\\(\\) with return type void returns Illuminate\\\\Database\\\\Eloquent\\\\Model but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addTaxonomies\\(\\) has invalid return type Modules\\\\Taxonomy\\\\Traits\\\\Collection\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:addTaxonomies\\(\\) should return Modules\\\\Taxonomy\\\\Traits\\\\Collection but returns \\$this\\(Modules\\\\User\\\\Entities\\\\User\\)\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:appliedJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:attachRelations\\(\\) should return array but returns false\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:authoredJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Job\\\\Entities\\\\Job\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:blacklistCompanies\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:candidates\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasMany\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:customRelation\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\CustomRelation\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:customRelation\\(\\) should return Modules\\\\User\\\\Entities\\\\CustomRelation but returns App\\\\Helpers\\\\CustomRelation\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:followedCompanies\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:followedJobs\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsToMany\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getAvatarAttribute\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\url\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getAvatarAttribute\\(\\) should return Modules\\\\User\\\\Entities\\\\url but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Traits\\\\Model\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) should return Modules\\\\User\\\\Entities\\\\Traits\\\\Model but returns Modules\\\\Company\\\\Entities\\\\Company\\|null\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getListIdCompaniesAttribute\\(\\) should return string but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getMetaCollectAttribute\\(\\) has invalid return type Collection\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getMetaCollectAttribute\\(\\) should return Collection but returns Illuminate\\\\Support\\\\Collection\\<int, Plank\\\\Metable\\\\Meta\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getReadyToApplyAttribute\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:getTermsAttribute\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), mixed\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:newestCandidate\\(\\) should return string but returns Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\HasOne\\<Modules\\\\Job\\\\Entities\\\\Candidate\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) has invalid return type mixed\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) should return mixed but returns array\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:prepare\\(\\) should return mixed but returns string\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeApproved\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeApproved\\(\\) should return Modules\\\\User\\\\Entities\\\\Traits\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\<Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeDoesntHaveApply\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeDoesntHaveApply\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeEmployers\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeEmployers\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeHaveApply\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeHaveApply\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeQuickSearch\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeQuickSearch\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeResumes\\(\\) has invalid return type Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:scopeResumes\\(\\) should return Modules\\\\User\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Builder but returns Illuminate\\\\Database\\\\Eloquent\\\\Builder\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Method Modules\\\\User\\\\Entities\\\\User\\:\\:setAvatarAttribute\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$name$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$params$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$query$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$terms$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$value$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @return with type Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\MorphMany is incompatible with native type string\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\#1 \\$input of function array_values expects array, mixed given\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\#3 \\$targets of class Overtrue\\\\LaravelFollow\\\\Events\\\\RelationAttached constructor expects array\\|int, stdClass given\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Parameter \\$expires_at of method Modules\\\\User\\\\Entities\\\\User\\:\\:addOneTerm\\(\\) has invalid type Modules\\\\Taxonomy\\\\Traits\\\\Carbon\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Property Modules\\\\User\\\\Entities\\\\User\\:\\:\\$arrayValues \\(array\\) on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Result of method Modules\\\\User\\\\Entities\\\\User\\:\\:setMeta\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Return type \\(Modules\\\\User\\\\Entities\\\\Traits\\\\Model\\) of method Modules\\\\User\\\\Entities\\\\User\\:\\:getCompanyForVerification\\(\\) should be compatible with return type \\(string\\) of method Modules\\\\User\\\\Entities\\\\Contracts\\\\MustApproveAccount\\:\\:getCompanyForVerification\\(\\)$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Strict comparison using \\=\\=\\= between false and array\\|null will always evaluate to false\\.$#"
			count: 2
			path: Modules/User/Entities/User.php

		-
			message: "#^Variable \\$taxonomies in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Entities/User.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\UserKeyword\\:\\:\\$url\\.$#"
			count: 1
			path: Modules/User/Entities/UserKeyword.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:user\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:user\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisActive\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement as its type\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisActive.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:announcement\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:announcement\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:\\$announcement \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement\\) does not accept Modules\\\\User\\\\Entities\\\\Announcement\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\:\\:\\$announcement has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\Announcement as its type\\.$#"
			count: 1
			path: Modules/User/Events/MeJobLikeThisProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:user\\(\\) has invalid return type Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Method Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:user\\(\\) should return Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job but returns Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\NewUserProcessed\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/User/Events/NewUserProcessed.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\UserHasRecentlyUpdateProfile\\:\\:\\$user \\(Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User\\) does not accept Modules\\\\User\\\\Entities\\\\User\\.$#"
			count: 1
			path: Modules/User/Events/UserHasRecentlyUpdateProfile.php

		-
			message: "#^Property Modules\\\\User\\\\Events\\\\UserHasRecentlyUpdateProfile\\:\\:\\$user has unknown class Modules\\\\User\\\\Events\\\\Modules\\\\User\\\\Entities\\\\User as its type\\.$#"
			count: 1
			path: Modules/User/Events/UserHasRecentlyUpdateProfile.php

		-
			message: "#^Variable \\$announce might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/AnnouncementController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:show\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:store\\(\\) has invalid return type Modules\\\\User\\\\Http\\\\Controllers\\\\Response\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserController\\:\\:store\\(\\) should return Modules\\\\User\\\\Http\\\\Controllers\\\\Response but returns Modules\\\\User\\\\Transformers\\\\UserResource\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^PHPDoc tag @param for parameter \\$request with type Illuminate\\\\Http\\\\Request is not subtype of native type Modules\\\\User\\\\Http\\\\Requests\\\\StoreUserRequest\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Variable \\$announced_jobs might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Variable \\$applied_jobs might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Variable \\$followed_companies might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Variable \\$followed_jobs might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\UserProfile\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProfileController.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Entities\\\\UserProfileParseProgress\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProfileController.php

		-
			message: "#^Anonymous function has an unused use \\$collectionName\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^Call to an undefined method Modules\\\\Job\\\\Repositories\\\\Contracts\\\\JobRepositoryInterface\\:\\:withoutCache\\(\\)\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^Comparison operation \"\\>\" between int\\<1, max\\> and 0 is always true\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserProgressController\\:\\:appliedJobs\\(\\) should return Modules\\\\Job\\\\Transformers\\\\JobFromEloquentResource but returns Redmix0901\\\\Core\\\\Http\\\\Responses\\\\BaseHttpResponse\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Controllers\\\\UserProgressController\\:\\:getEmployeesOfCompany\\(\\) should return Modules\\\\Company\\\\Transformers\\\\CompanyFromEloquentResource but returns Redmix0901\\\\Core\\\\Http\\\\Responses\\\\BaseHttpResponse\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$keyword$#"
			count: 3
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$page$#"
			count: 3
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$page_size$#"
			count: 3
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$status$#"
			count: 3
			path: Modules/User/Http/Controllers/UserProgressController.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserResumeController.php

		-
			message: "#^Variable \\$dataRelation on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserResumeController.php

		-
			message: "#^Variable \\$key might not be defined\\.$#"
			count: 1
			path: Modules/User/Http/Controllers/UserResumeController.php

		-
			message: "#^Method Modules\\\\User\\\\Http\\\\Middleware\\\\EnsureAccountApproved\\:\\:handle\\(\\) should return Illuminate\\\\Http\\\\RedirectResponse\\|Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\|void\\.$#"
			count: 1
			path: Modules/User/Http/Middleware/EnsureAccountApproved.php

		-
			message: "#^Result of function abort \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Http/Middleware/EnsureAccountApproved.php

		-
			message: "#^Array has 2 duplicate keys with value 'experiences\\.\\*\\.position' \\('experiences\\.\\*\\.position', 'experiences\\.\\*\\.position'\\)\\.$#"
			count: 1
			path: Modules/User/Http/Requests/PatchUserProfileRequest.php

		-
			message: "#^Property Modules\\\\User\\\\Http\\\\Requests\\\\SearchCandidateRequest\\:\\:\\$type is never read, only written\\.$#"
			count: 1
			path: Modules/User/Http/Requests/SearchCandidateRequest.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\CreateUserProcess\\:\\:handle\\(\\) with return type void returns Modules\\\\User\\\\Entities\\\\User but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/CreateUserProcess.php

		-
			message: "#^Parameter \\$exception of method Modules\\\\User\\\\Jobs\\\\CreateUserProcess\\:\\:failed\\(\\) has invalid type Modules\\\\User\\\\Jobs\\\\Exception\\.$#"
			count: 2
			path: Modules/User/Jobs/CreateUserProcess.php

		-
			message: "#^Property Modules\\\\User\\\\Jobs\\\\DeleteUserProcess\\:\\:\\$id is never read, only written\\.$#"
			count: 1
			path: Modules/User/Jobs/DeleteUserProcess.php

		-
			message: "#^Property Modules\\\\User\\\\Jobs\\\\DeleteUserProcess\\:\\:\\$params is unused\\.$#"
			count: 1
			path: Modules/User/Jobs/DeleteUserProcess.php

		-
			message: "#^Property Modules\\\\User\\\\Jobs\\\\DeleteUserProcess\\:\\:\\$userRepository is never read, only written\\.$#"
			count: 1
			path: Modules/User/Jobs/DeleteUserProcess.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/User/Jobs/DeleteUserProcess.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getEmail\\(\\) with return type void returns array\\<int, mixed\\>\\|null but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getPhone\\(\\) with return type void returns array\\<int, string\\> but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$content\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 3
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Result of method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getEmail\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Result of method Modules\\\\User\\\\Jobs\\\\DetectFileProcess\\:\\:getPhone\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: Modules/User/Jobs/DetectFileProcess.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Jobs\\\\UpdateUserProcess\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Call to method all\\(\\) on an unknown class Modules\\\\User\\\\Jobs\\\\StoreCompanyRequest\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Call to method user\\(\\) on an unknown class Modules\\\\User\\\\Jobs\\\\StoreCompanyRequest\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Constructor of class Modules\\\\User\\\\Jobs\\\\UpdateUserProcess has an unused parameter \\$author\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Method Modules\\\\User\\\\Jobs\\\\UpdateUserProcess\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Parameter \\$request of method Modules\\\\User\\\\Jobs\\\\UpdateUserProcess\\:\\:fromRequest\\(\\) has invalid type Modules\\\\User\\\\Jobs\\\\StoreCompanyRequest\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Property Modules\\\\User\\\\Jobs\\\\UpdateUserProcess\\:\\:\\$author is unused\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Property Modules\\\\User\\\\Jobs\\\\UpdateUserProcess\\:\\:\\$user is never read, only written\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/User/Jobs/UpdateUserProcess.php

		-
			message: "#^Call to method activity\\(\\) on an unknown class Modules\\\\Activity\\\\Events\\\\ClickApplyProcessed\\.$#"
			count: 4
			path: Modules/User/Listeners/ClickApplyJob.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Activity\\\\Events\\\\ClickApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/ClickApplyJob.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\ClickApplyJob\\:\\:handle\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\ClickApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/ClickApplyJob.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\ClickApplyJob\\:\\:shouldQueue\\(\\) has invalid type Modules\\\\Activity\\\\Events\\\\ClickApplyProcessed\\.$#"
			count: 2
			path: Modules/User/Listeners/ClickApplyJob.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Overtrue\\\\LaravelFollow\\\\Events\\\\RelationAttached\\.$#"
			count: 1
			path: Modules/User/Listeners/FollowedJob.php

		-
			message: "#^Parameter \\#1 \\$message of static method Illuminate\\\\Log\\\\Logger\\:\\:info\\(\\) expects string, array given\\.$#"
			count: 1
			path: Modules/User/Listeners/FollowedJob.php

		-
			message: "#^Access to property \\$users on an unknown class Modules\\\\User\\\\Events\\\\Modules\\\\Job\\\\Entities\\\\Job\\.$#"
			count: 1
			path: Modules/User/Listeners/MeJobLikeThis.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\User\\\\Events\\\\MeJobLikeThisProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/MeJobLikeThis.php

		-
			message: "#^Call to method candidates\\(\\) on an unknown class Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 8
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Method Modules\\\\User\\\\Listeners\\\\NewApply\\:\\:handle\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type Modules\\\\User\\\\Events\\\\NewApplyProcessed is not subtype of native type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\NewApply\\:\\:handle\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\NewApply\\:\\:shouldQueue\\(\\) has invalid type Modules\\\\Job\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\NewApply\\:\\:shouldQueue\\(\\) has invalid type Modules\\\\User\\\\Events\\\\NewApplyProcessed\\.$#"
			count: 1
			path: Modules/User/Listeners/NewApply.php

		-
			message: "#^Call to method company\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\NewUserRegister\\.$#"
			count: 1
			path: Modules/User/Listeners/NotifyNewUserRegister.php

		-
			message: "#^Call to method user\\(\\) on an unknown class Modules\\\\User\\\\Events\\\\NewUserRegister\\.$#"
			count: 1
			path: Modules/User/Listeners/NotifyNewUserRegister.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\User\\\\Events\\\\NewUserRegister\\.$#"
			count: 1
			path: Modules/User/Listeners/NotifyNewUserRegister.php

		-
			message: "#^Parameter \\$event of method Modules\\\\User\\\\Listeners\\\\NotifyNewUserRegister\\:\\:handle\\(\\) has invalid type Modules\\\\User\\\\Events\\\\NewUserRegister\\.$#"
			count: 1
			path: Modules/User/Listeners/NotifyNewUserRegister.php

		-
			message: "#^PHPDoc tag @param for parameter \\$event with type object is not subtype of native type Modules\\\\User\\\\Events\\\\UserProfileCvUploaded\\.$#"
			count: 1
			path: Modules/User/Listeners/ParseUserProfileCvUpload.php

		-
			message: "#^Call to method listen\\(\\) on an unknown class Modules\\\\User\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 6
			path: Modules/User/Listeners/PublishUserEvent.php

		-
			message: "#^Method Modules\\\\User\\\\Listeners\\\\PublishUserEvent\\:\\:subscribe\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Listeners/PublishUserEvent.php

		-
			message: "#^Parameter \\$events of method Modules\\\\User\\\\Listeners\\\\PublishUserEvent\\:\\:subscribe\\(\\) has invalid type Modules\\\\User\\\\Listeners\\\\Illuminate\\\\Events\\\\Dispatcher\\.$#"
			count: 1
			path: Modules/User/Listeners/PublishUserEvent.php

		-
			message: "#^Anonymous function has an unused use \\$keyword\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/AuthenticationLogEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/AuthenticationLogEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/AuthenticationLogEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$query of class ONGR\\\\ElasticsearchDSL\\\\Query\\\\FullText\\\\MatchQuery constructor expects string, int given\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/AuthenticationLogEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/UserEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/UserEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 2
			path: Modules/User/Repositories/Eloquents/UserResumeEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 2
			path: Modules/User/Repositories/Eloquents/UserResumeEloquentRepository.php

		-
			message: "#^Parameter \\#2 \\$query of class ONGR\\\\ElasticsearchDSL\\\\Query\\\\FullText\\\\MatchQuery constructor expects string, int given\\.$#"
			count: 1
			path: Modules/User/Repositories/Eloquents/UserResumeEloquentRepository.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\AnnouncementResource\\:\\:\\$location\\.$#"
			count: 1
			path: Modules/User/Transformers/AnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\AnnouncementResource\\:\\:\\$skill\\.$#"
			count: 1
			path: Modules/User/Transformers/AnnouncementResource.php

		-
			message: "#^Call to an undefined method Modules\\\\User\\\\Transformers\\\\AnnouncementResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/User/Transformers/AnnouncementResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/AnnouncementResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$auto_apply\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$avatar_url\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$birthday\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$changeable_password\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$company_id\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$cover_letter\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$educations\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$experiences\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$gender\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$id\\.$#"
			count: 2
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$position\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$recent_position\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$social_networks\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$sort_name\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$taxonomies\\.$#"
			count: 2
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:\\$willing_to_work\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Call to an undefined method Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:getTaxonomies\\(\\)\\.$#"
			count: 4
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Call to an undefined method Modules\\\\User\\\\Transformers\\\\CurrentUserResource\\:\\:isAllowShareCV\\(\\)\\.$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/CurrentUserResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\EmployerCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerCollection.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\EmployerCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployerResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployerResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployerResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployerResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\EmployerResource\\:\\:\\$position\\.$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/EmployerResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$province\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$user\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\GetUserProfileResource\\:\\:\\$years_of_exp\\.$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 2
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/GetUserProfileResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\ResumeCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$addresses\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$applied_jobs\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$authored_jobs\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$blacklist_companies\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$bottom_cv_questions\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$company_id\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$email\\.$#"
			count: 3
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$expect_skills\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$extra_skills_arr\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$extra_skills_ids\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$files_cv\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$files_cvbuilder\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$followed_companies\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$followed_jobs\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$id\\.$#"
			count: 7
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$phone\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$resume_ids\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$salary_range\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$salary_range_expect\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$skills\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$skills_id\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$skype\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$source\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$status_display\\.$#"
			count: 2
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$status_works\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$status_works_expect\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$username\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$uuid\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\ResumeResource\\:\\:\\$years_of_exp\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Dead catch \\- Exception is never thrown in the try block\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Unsafe usage of new static\\(\\)\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Variable \\$activities on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^Variable \\$tests on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: Modules/User/Transformers/ResumeResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/UserLogResource.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\UserProfileCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/User/Transformers/UserProfileCollection.php

		-
			message: "#^Method Modules\\\\User\\\\Transformers\\\\UserProfileCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/User/Transformers/UserProfileCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/User/Transformers/UserProfileCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserProfileResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/UserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserProfileResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/UserProfileResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/UserProfileResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$full_name\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResource\\:\\:\\$id\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/UserResource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$created_at\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$email\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$json\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$progress_value\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$resume_id\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$resume_name\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$updated_at\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\User\\\\Transformers\\\\UserResumeReource\\:\\:\\$user_id\\.$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/User/Transformers/UserResumeReource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$city\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$latitude\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$longitude\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$postal_code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$state\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\Address\\:\\:\\$street\\.$#"
			count: 3
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$name_with_type\\.$#"
			count: 3
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$path_with_type\\.$#"
			count: 3
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$postal_code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^If condition is always false\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Ternary operator condition is always true\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/Address.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$name\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$name_with_type\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$parent\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$parent_code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$path\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$path_with_type\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$postal_code\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$slug\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$status\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:\\$type\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Method Modules\\\\VietnamArea\\\\Entities\\\\VietnamArea\\:\\:scopeApplyStatus\\(\\) with return type void returns mixed but should not return anything\\.$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$builder$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$model$#"
			count: 1
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @var above a method has no effect\\.$#"
			count: 3
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @var contains unknown class Modules\\\\VietnamArea\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\.$#"
			count: 2
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @var contains unknown class Modules\\\\VietnamArea\\\\Entities\\\\Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\hasMany\\.$#"
			count: 4
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^PHPDoc tag @var does not specify variable name\\.$#"
			count: 3
			path: Modules/VietnamArea/Entities/VietnamArea.php

		-
			message: "#^Property Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\BaseCacheDecorator\\:\\:\\$cache_time \\(Redmix0901\\\\Core\\\\Repositories\\\\Caches\\\\cache_time\\) does not accept int\\.$#"
			count: 1
			path: Modules/VietnamArea/Repositories/Caches/AreaCacheDecorator.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:search\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Repositories/Eloquents/AreaEloquentRepository.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Database\\\\Eloquent\\\\Model\\:\\:searchableAs\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Repositories/Eloquents/AreaEloquentRepository.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$display_name\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$district_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$full_address\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$province_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$street\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:\\$ward_id\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Call to an undefined method Modules\\\\VietnamArea\\\\Transformers\\\\AddressResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AddressResource.php

		-
			message: "#^Method Modules\\\\VietnamArea\\\\Transformers\\\\AreaCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AreaCollection.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AreaResource\\:\\:\\$code\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AreaResource.php

		-
			message: "#^Access to an undefined property Modules\\\\VietnamArea\\\\Transformers\\\\AreaResource\\:\\:\\$name_with_type\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AreaResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: Modules/VietnamArea/Transformers/AreaResource.php

		-
			message: "#^Method Modules\\\\VietnamArea\\\\Transformers\\\\FilterAreaCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/FilterAreaCollection.php

		-
			message: "#^Method Modules\\\\VietnamArea\\\\Transformers\\\\FilterAreaCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/FilterAreaCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: Modules/VietnamArea/Transformers/FilterAreaCollection.php

		-
			message: "#^Call to function is_null\\(\\) with Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable will always evaluate to false\\.$#"
			count: 1
			path: app/Auth/ApiSsoGuard.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/Auth/ApiSsoGuard.php

		-
			message: "#^Method App\\\\Auth\\\\ElasticsearchUserProvider\\:\\:retrieveByCredentials\\(\\) should return Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable\\|null but returns Illuminate\\\\Database\\\\Eloquent\\\\Model\\.$#"
			count: 1
			path: app/Auth/ElasticsearchUserProvider.php

		-
			message: "#^PHPDoc tag @param for parameter \\$user with type Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable\\|Illuminate\\\\Database\\\\Eloquent\\\\Model is not subtype of native type Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable\\.$#"
			count: 1
			path: app/Auth/ElasticsearchUserProvider.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$content$#"
			count: 1
			path: app/Channels/Messages/TelegramMessages.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Notifications\\\\Notification\\:\\:toTelegram\\(\\)\\.$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Method App\\\\Channels\\\\TelegramChannel\\:\\:send\\(\\) with return type void returns array\\<int, mixed\\> but should not return anything\\.$#"
			count: 1
			path: app/Channels/TelegramChannel.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 2
			path: app/Console/Commands/ImportQnA.php

		-
			message: "#^Parameter \\#1 \\$time of method Illuminate\\\\Console\\\\Scheduling\\\\Event\\:\\:dailyAt\\(\\) expects string, int given\\.$#"
			count: 1
			path: app/Console/Kernel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addFile\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addMetaCollection\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:addTaxonomies\\(\\)\\.$#"
			count: 1
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:fileAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:metaAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Call to an undefined method \\$this\\(App\\\\Contracts\\\\TopdevModel\\)\\:\\:termAttribute\\(\\)\\.$#"
			count: 2
			path: app/Contracts/TopdevModel.php

		-
			message: "#^Method App\\\\Exceptions\\\\Handler\\:\\:render\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 3
			path: app/Exceptions/Handler.php

		-
			message: "#^Method App\\\\Exceptions\\\\Handler\\:\\:render\\(\\) should return Illuminate\\\\Http\\\\Response but returns Symfony\\\\Component\\\\HttpFoundation\\\\Response\\.$#"
			count: 1
			path: app/Exceptions/Handler.php

		-
			message: "#^Call to an undefined method App\\\\Helpers\\\\CustomRelation\\:\\:first\\(\\)\\.$#"
			count: 1
			path: app/Helpers/CustomRelation.php

		-
			message: "#^Access to an undefined property App\\\\Helpers\\\\FakeDataElastic\\:\\:\\$sizeFake\\.$#"
			count: 1
			path: app/Helpers/FakeDataElastic.php

		-
			message: "#^Method App\\\\Helpers\\\\Helpers\\:\\:makeCollectionList\\(\\) should return string but returns null\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^Method App\\\\Helpers\\\\Helpers\\:\\:stripVietnamese\\(\\) with return type void returns \\(array\\|string\\|null\\) but should not return anything\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^Method App\\\\Helpers\\\\Helpers\\:\\:toSlug\\(\\) with return type void returns string\\|null but should not return anything\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^PHPDoc tag @param for parameter \\$collection with type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection is not subtype of native type Illuminate\\\\Database\\\\Eloquent\\\\Collection\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$str\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$string\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 2
			path: app/Helpers/Helpers.php

		-
			message: "#^Parameter \\$collection of method App\\\\Helpers\\\\Helpers\\:\\:makeCollectionList\\(\\) has invalid type App\\\\Helpers\\\\Illuminate\\\\Database\\\\Eloquent\\\\Collection\\.$#"
			count: 1
			path: app/Helpers/Helpers.php

		-
			message: "#^Variable \\$collection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 2
			path: app/Helpers/Helpers.php

		-
			message: "#^Function func_get_args invoked with 1 parameter, 0 required\\.$#"
			count: 1
			path: app/Helpers/QueueHeplers.php

		-
			message: "#^Variable \\$fcmToken on left side of \\?\\? always exists and is not nullable\\.$#"
			count: 1
			path: app/Helpers/QueueHeplers.php

		-
			message: "#^Call to static method create\\(\\) on an unknown class App\\\\User\\.$#"
			count: 1
			path: app/Http/Controllers/Auth/RegisterController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\Auth\\\\RegisterController\\:\\:create\\(\\) has invalid return type App\\\\User\\.$#"
			count: 1
			path: app/Http/Controllers/Auth/RegisterController.php

		-
			message: "#^Anonymous function has an unused use \\$data\\.$#"
			count: 1
			path: app/Http/Controllers/DiscoveryUnitsController.php

		-
			message: "#^Variable \\$arrText might not be defined\\.$#"
			count: 1
			path: app/Http/Controllers/DiscoveryUnitsController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\GetReportController\\:\\:store\\(\\) should return Illuminate\\\\Http\\\\Response but returns Illuminate\\\\Http\\\\JsonResponse\\.$#"
			count: 1
			path: app/Http/Controllers/GetReportController.php

		-
			message: "#^Method App\\\\Http\\\\Controllers\\\\PromotionalBannerController\\:\\:index\\(\\) should return Illuminate\\\\Http\\\\Response but returns App\\\\Http\\\\Resources\\\\PromotionalBannerResource\\.$#"
			count: 1
			path: app/Http/Controllers/PromotionalBannerController.php

		-
			message: "#^Access to an undefined property Modules\\\\Taxonomy\\\\Entities\\\\Terms\\:\\:\\$name\\.$#"
			count: 1
			path: app/Http/Controllers/QnAController.php

		-
			message: "#^Method App\\\\Http\\\\Middleware\\\\Authenticate\\:\\:redirectTo\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: app/Http/Middleware/Authenticate.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$guard$#"
			count: 1
			path: app/Http/Middleware/PermissionUser.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$success\\.$#"
			count: 1
			path: app/Http/Middleware/StoreRequest.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\CompanySuggestResource\\:\\:\\$display_name\\.$#"
			count: 3
			path: app/Http/Resources/CompanySuggestResource.php

		-
			message: "#^Call to an undefined method App\\\\Http\\\\Resources\\\\CompanySuggestResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: app/Http/Resources/CompanySuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\JobSuggestResource\\:\\:\\$title\\.$#"
			count: 2
			path: app/Http/Resources/JobSuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\JobSuggestResource\\:\\:\\$uuid\\.$#"
			count: 1
			path: app/Http/Resources/JobSuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\QnaTaxonomyResource\\:\\:\\$id\\.$#"
			count: 1
			path: app/Http/Resources/QnaTaxonomyResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\QnaTaxonomyResource\\:\\:\\$slug\\.$#"
			count: 1
			path: app/Http/Resources/QnaTaxonomyResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\QnaTaxonomyResource\\:\\:\\$sort_order\\.$#"
			count: 1
			path: app/Http/Resources/QnaTaxonomyResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\QnaTaxonomyResource\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: app/Http/Resources/QnaTaxonomyResource.php

		-
			message: "#^Method App\\\\Http\\\\Resources\\\\SuggestResource\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Support\\\\Collection\\<\\(int\\|string\\), App\\\\Http\\\\Resources\\\\CompanySuggestResource\\|App\\\\Http\\\\Resources\\\\JobSuggestResource\\|App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\>\\.$#"
			count: 1
			path: app/Http/Resources/SuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:\\$taxonomy\\.$#"
			count: 1
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Access to an undefined property App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:\\$term\\.$#"
			count: 3
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Call to an undefined method App\\\\Http\\\\Resources\\\\TaxonomySuggestResource\\:\\:getKey\\(\\)\\.$#"
			count: 1
			path: app/Http/Resources/TaxonomySuggestResource.php

		-
			message: "#^Property App\\\\Jobs\\\\DeleteFileCvBuilder\\:\\:\\$resume_id is never read, only written\\.$#"
			count: 1
			path: app/Jobs/DeleteFileCvBuilder.php

		-
			message: "#^Property App\\\\Jobs\\\\ProcessGetReport\\:\\:\\$params is never read, only written\\.$#"
			count: 1
			path: app/Jobs/ProcessGetReport.php

		-
			message: "#^Property App\\\\Jobs\\\\ProcessVMD2022Promotion\\:\\:\\$params is never read, only written\\.$#"
			count: 1
			path: app/Jobs/ProcessVMD2022Promotion.php

		-
			message: "#^Property App\\\\Jobs\\\\ProcessVWS2022Promotion\\:\\:\\$params is never read, only written\\.$#"
			count: 1
			path: app/Jobs/ProcessVWS2022Promotion.php

		-
			message: "#^Property App\\\\Jobs\\\\SendRabbitmqEvent\\:\\:\\$origin is never read, only written\\.$#"
			count: 1
			path: app/Jobs/SendRabbitmqEvent.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchCompanyIndustry.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchJobIndustry.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchLevel.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchLocation.php

		-
			message: "#^Result of static method App\\\\Helpers\\\\Helpers\\:\\:stripVietnamese\\(\\) \\(void\\) is used\\.$#"
			count: 1
			path: app/Metas/MetaSearchLocation.php

		-
			message: "#^Result of static method App\\\\Helpers\\\\Helpers\\:\\:stripVietnamese\\(\\) \\(void\\) is used\\.$#"
			count: 2
			path: app/Metas/MetaSearchLocationSkill.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchLocationType.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 1
			path: app/Metas/MetaSearchType.php

		-
			message: "#^Call to private method withoutGlobalScopes\\(\\) of parent class Illuminate\\\\Database\\\\Eloquent\\\\Relations\\\\BelongsTo\\<TRelatedModel of Illuminate\\\\Database\\\\Eloquent\\\\Model,TChildModel of Illuminate\\\\Database\\\\Eloquent\\\\Model\\>\\.$#"
			count: 1
			path: app/Models/QnaTermRelationship.php

		-
			message: "#^Access to an undefined property App\\\\Traits\\\\Concerns\\\\Salary\\:\\:\\$value\\.$#"
			count: 1
			path: app/Traits/Concerns/Salary.php

		-
			message: "#^Method App\\\\Transformers\\\\BlogQnaCollection\\:\\:toArray\\(\\) should return array but returns Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\.$#"
			count: 1
			path: app/Transformers/BlogQnaCollection.php

		-
			message: "#^Method App\\\\Transformers\\\\BlogQnaCollection\\:\\:withResponse\\(\\) should return array but return statement is missing\\.$#"
			count: 1
			path: app/Transformers/BlogQnaCollection.php

		-
			message: "#^Variable \\$elasticCollection in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: app/Transformers/BlogQnaCollection.php

		-
			message: "#^Access to an undefined property App\\\\Transformers\\\\BlogQnaResource\\:\\:\\$id\\.$#"
			count: 1
			path: app/Transformers/BlogQnaResource.php

		-
			message: "#^Access to an undefined property App\\\\Transformers\\\\BlogQnaResource\\:\\:\\$title\\.$#"
			count: 1
			path: app/Transformers/BlogQnaResource.php

		-
			message: "#^Access to an undefined property App\\\\Transformers\\\\BlogQnaResource\\:\\:\\$url\\.$#"
			count: 1
			path: app/Transformers/BlogQnaResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: app/Transformers/BlogQnaResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: app/Transformers/MenuAreaResource.php

		-
			message: "#^Call to an undefined method Illuminate\\\\Http\\\\Resources\\\\Json\\\\AnonymousResourceCollection\\:\\:first\\(\\)\\.$#"
			count: 3
			path: app/Transformers/MenuCollection.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: app/Transformers/MenuTopdev.php

		-
			message: "#^Access to an undefined property App\\\\Transformers\\\\MenuTotalResource\\:\\:\\$companies\\.$#"
			count: 1
			path: app/Transformers/MenuTotalResource.php

		-
			message: "#^Access to an undefined property App\\\\Transformers\\\\MenuTotalResource\\:\\:\\$jobs\\.$#"
			count: 1
			path: app/Transformers/MenuTotalResource.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\\\Illuminate\\\\Http\\\\Request\\)\\: Unexpected token \"\\\\n     \\* \", expected variable at offset [0-9]+$#"
			count: 1
			path: app/Transformers/MenuTotalResource.php

		-
			message: "#^Call to function is_null\\(\\) with string will always evaluate to false\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^Else branch is unreachable because ternary operator condition is always true\\.$#"
			count: 3
			path: app/helpers.php

		-
			message: "#^Function admin_trans\\(\\) never returns Illuminate\\\\Contracts\\\\Translation\\\\Translator so it can be removed from the return type\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^Function admin_trans\\(\\) never returns array so it can be removed from the return type\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^Function file_name_random\\(\\) never returns Illuminate\\\\Contracts\\\\Routing\\\\UrlGenerator so it can be removed from the return type\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^Function file_taxonomies invoked with 1 parameter, 0 required\\.$#"
			count: 4
			path: app/helpers.php

		-
			message: "#^Function seo_friendly_url\\(\\) never returns Illuminate\\\\Contracts\\\\Routing\\\\UrlGenerator so it can be removed from the return type\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[array\\] \\$param\\)\\: Unexpected token \"\\]\", expected '\\(' at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[string\\] \\$param\\[utm_campaign\\] \\=\\>  Tên chiến dịch riêng lẻ, khẩu hiệu, mã khuyến mại, v\\.v\\. cho sản phẩm\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[string\\] \\$param\\[utm_content\\] \\=\\>  Được sử dụng để phân biệt nội dung tương tự hoặc các liên kết trong cùng một quảng cáo\\. Ví dụ\\: nếu có hai liên kết gọi hành động trong cùng một thông báo email, thì bạn có thể sử dụng utm_content và đặt các giá trị khác nhau cho từng liên kết để có thể biết phiên bản nào có hiệu quả hơn\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[string\\] \\$param\\[utm_medium\\] \\=\\>  Phương tiện quảng cáo hoặc tiếp thị, ví dụ\\: cpc, biểu ngữ, bản tin email\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[string\\] \\$param\\[utm_source\\] \\=\\>  Xác định nhà quảng cáo, trang web, ấn phẩm, v\\.v\\. đang gửi lưu lượng truy cập đến thuộc tính của bạn, ví dụ\\: google, bản tin 4, biển quảng cáo\\.\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[string\\] \\$param\\[utm_term\\] \\=\\>  Xác định từ khóa tìm kiếm có trả tiền\\. Nếu bạn gắn thẻ chiến dịch từ khóa phải trả tiền theo cách thủ công, thì bạn cũng nên sử dụng utm_term để chỉ định từ khóa\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\[type\\] \\$link\\)\\: Unexpected token \"\\[\", expected type at offset [0-9]+$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$message$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$parameters$#"
			count: 2
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$path$#"
			count: 2
			path: app/helpers.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$secure$#"
			count: 2
			path: app/helpers.php

		-
			message: "#^Parameter \\#1 \\$version of class DOMDocument constructor expects string, null given\\.$#"
			count: 1
			path: app/helpers.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: app/helpers.php

		- "#^Function factory invoked with [0-9] parameters, 0 required\\.$#"
		- "#^Function factory invoked with [0-9] parameter, 0 required\\.$#"
		- "#^Access to an undefined property Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable\\:\\:*#"
		- "#^Call to an undefined method Illuminate\\\\Contracts\\\\Auth\\\\Authenticatable\\:\\:*#"
